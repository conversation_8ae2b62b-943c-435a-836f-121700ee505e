const { Api, JsonRpc } = require('eosjs');
const { JsSignatureProvider } = require('eosjs/dist/eosjs-jssig');
const fetch = require('node-fetch');
const { TextEncoder, TextDecoder } = require('util');

// --- Configuration ---
const payerPrivateKey = '5KDodhQoSPs2PqaGvRVYR12W1B7aUSZrqa1M8rirp2uJWHFXLXp'; // waxprofitnew's private key
const payerAccount = 'waxprofitnew';
const receiverAccount = 'blockvestwax';
const ramBytes = 30720; // 30 KB
const rpcEndpoint = 'https://wax.greymass.com';

// --- Script ---
const signatureProvider = new JsSignatureProvider([payerPrivateKey]);
const rpc = new JsonRpc(rpcEndpoint, { fetch });
const api = new Api({ rpc, signatureProvider, textDecoder: new TextDecoder(), textEncoder: new TextEncoder() });

async function main() {
    try {
        console.log(`Buying ${ramBytes} bytes of RAM for ${receiverAccount}, paid by ${payerAccount}...`);

        const result = await api.transact({
            actions: [{
                account: 'eosio',
                name: 'buyrambytes',
                authorization: [{
                    actor: payerAccount,
                    permission: 'active',
                }],
                data: {
                    payer: payerAccount,
                    receiver: receiverAccount,
                    bytes: ramBytes,
                },
            }]
        }, {
            blocksBehind: 3,
            expireSeconds: 30,
        });

        console.log('RAM purchase successful!');
        console.log(`Transaction ID: ${result.transaction_id}`);
        console.log(`View on explorer: https://wax.bloks.io/transaction/${result.transaction_id}`);

    } catch (error) {
        console.error('Error buying RAM:', error.json ? JSON.stringify(error.json, null, 2) : error);
    }
}

main();
