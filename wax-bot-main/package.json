{"name": "wax-profit-bot", "version": "1.0.0", "description": "Off-chain bot to manage WAX contract interactions.", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "build": "node scripts/build-deployment.js", "build-protected": "node scripts/create-protected-deployment.js", "deploy-setup": "node scripts/deploy-setup.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"cross-fetch": "^4.1.0", "date-fns": "^4.1.0", "dotenv": "^16.3.1", "eosjs": "^22.1.0", "lowdb": "^1.0.0", "moment": "^2.29.1", "telegraf": "^4.16.3"}, "devDependencies": {"javascript-obfuscator": "^4.1.1", "nodemon": "^3.1.10"}}