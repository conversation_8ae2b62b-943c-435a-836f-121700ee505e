# Wax Profit Payout Bot

This is a sophisticated Telegram bot designed to automate the process of calculating and sending profit-sharing payouts on the WAX blockchain. It monitors a designated WAX vault account for incoming deposits, schedules payouts with a randomized delay, and provides comprehensive administrative controls and notifications via Telegram.

The bot is built with a robust, persistent queueing system to ensure no payouts are lost, even if the bot restarts or encounters an error.

## Key Features

- **Automated Payout Processing:** Monitors a WAX account and automatically processes payouts for incoming deposits.
- **Profit Calculation:** Calculates a 1.5% profit on top of the initial deposit for the final payout amount.
- **Persistent Payout Queue:** Uses a `lowdb` JSON database (`db.json`) to ensure that scheduled payouts survive bot restarts and failures.
- **Intelligent Retry Logic:** Automatically retries failed payouts up to a configurable limit (`MAX_RETRIES`).
- **Admin Notifications:** Provides detailed, real-time notifications to Telegram admins for key events (deposits received, payouts scheduled, payouts sent, errors).
- **Error Handling:** Parses common blockchain errors (insufficient RAM, CPU, NET, balance) into user-friendly messages.
- **Comprehensive Admin Commands:** A full suite of Telegram commands to manage the bot.

## Admin Commands

- `/help`: Displays the list of all available commands.
- `/status`: Shows the current system status, including pause state and blacklisted memos.
- `/pause`: Immediately pauses all automatic payout processing.
- `/resume`: Resumes normal payout processing.
- `/resources`: Checks the WAX balance, RAM, CPU, and NET status of the vault account.
- `/retrypayout <memo>`: Manually re-queues a lost or failed payout by its original memo.
- `/blist <memo>`: Adds a memo to the blacklist, preventing future payouts.
- `/unblist <memo>`: Removes a memo from the blacklist.
- `/addadmin <id>`: Adds a new Telegram user as an admin.
- `/deladmin <id>`: Removes an admin.
- `/listadmins`: Lists all current admin IDs.
- `/clearall`: Wipes all transactional data (trades, blacklist, etc.). **Use with extreme caution!**

## Setup and Installation

### 1. Prerequisites

- Node.js (v14 or higher)
- npm

### 2. Local Setup

1.  **Clone the repository:**
    ```bash
    git clone <your-repo-url>
    cd wax-bot
    ```

2.  **Install dependencies:**
    ```bash
    npm install
    ```

3.  **Create the environment file:**
    Create a file named `.env` in the root of the `wax-bot` directory and add the necessary environment variables (see below).

4.  **Run the bot:**
    ```bash
    node src/index.js
    ```

## Environment Variables

Create a `.env` file in the project root with the following variables:

```env
# Your Telegram bot's token from BotFather
TELEGRAM_BOT_TOKEN=your_telegram_bot_token

# Your Telegram user ID(s), comma-separated for multiple admins
TELEGRAM_ADMIN_ID=your_user_id

# The WAX account the bot will monitor for incoming transfers
WAX_VAULT_ACCOUNT=yourwaxvault

# The WAX account that will send the payouts (can be the same as the vault)
WAX_PAYOUT_ACCOUNT=yourwaxpayout

# The ACTIVE private key for the WAX_PAYOUT_ACCOUNT
WAX_PAYOUT_PRIVATE_KEY=your_payout_account_private_key

# The WAX API endpoint to use
WAX_API_ENDPOINT=https://wax.greymass.com

# The destination account for payouts (default: bybitwaxonly)
WAX_PAYOUT_DESTINATION=bybitwaxonly

# (Optional) For deployment, path to the persistent database file
# DATABASE_PATH=/data/db.json
```

## Deployment to Render

This bot is designed to be easily deployed on a platform like [Render](https://render.com/).

### 1. Create a "Web Service" on Render

- Connect your GitHub repository.
- **Runtime:** `Node`
- **Build Command:** `npm install`
- **Start Command:** `node src/index.js`

### 2. Add a Persistent Disk

This is **critical** to prevent your database from being wiped on every deploy.

- Go to the "Advanced" section of your service settings.
- **Add Disk**:
    - **Name:** `database`
    - **Mount Path:** `/data`
    - **Size:** 1 GB (is sufficient)

### 3. Add Environment Variables

- Go to the "Environment" tab.
- Add all the variables from your `.env` file as individual environment variables.
- **Crucially, add the `DATABASE_PATH` variable**:
    - **Key:** `DATABASE_PATH`
    - **Value:** `/data/db.json`

This configuration tells the bot to store its database file on the persistent disk you created, ensuring data integrity across deployments.
