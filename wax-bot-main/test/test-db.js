#!/usr/bin/env node

/**
 * Simple test script to verify database functions work correctly
 */

require('dotenv').config();
const db = require('../src/db');

async function testDatabase() {
    console.log('🧪 Testing Database Functions...\n');
    
    try {
        // Initialize database
        console.log('1. Initializing database...');
        await db.initializeDb();
        console.log('✅ Database initialized\n');
        
        // Test getTradesByMemo function
        console.log('2. Testing getTradesByMemo function...');
        const testMemo = '10003050';
        const trades = db.getTradesByMemo(testMemo);
        console.log(`✅ Found ${trades.length} trades for memo "${testMemo}"`);
        if (trades.length > 0) {
            console.log(`   First trade: ${trades[0].from} -> ${trades[0].quantity} (${trades[0].status})`);
        }
        console.log('');
        
        // Test memo functions
        console.log('3. Testing memo management...');
        const testMemoRecord = await db.getMemo(testMemo);
        if (testMemoRecord) {
            console.log(`✅ Memo record found: blocked=${testMemoRecord.is_blocked}`);
        } else {
            console.log(`ℹ️  No memo record found for "${testMemo}"`);
        }
        
        const blacklistedMemos = db.getBlacklistedMemos();
        console.log(`✅ Found ${blacklistedMemos.length} blacklisted memos\n`);
        
        // Test admin functions
        console.log('4. Testing admin management...');
        const admins = db.getAdmins();
        console.log(`✅ Found ${admins.length} admins: ${admins.join(', ')}\n`);
        
        // Test payout queue functions
        console.log('5. Testing payout queue...');
        const pendingPayouts = db.getPendingPayouts();
        console.log(`✅ Found ${pendingPayouts.length} pending payouts`);
        
        // Note: failed_payouts is internal to db module, we'll just note this
        console.log(`ℹ️  Failed payouts are tracked internally\n`);
        
        // Test system state
        console.log('6. Testing system state...');
        const lastBlock = db.getLastProcessedBlock();
        console.log(`✅ Last processed block: ${lastBlock}\n`);
        
        console.log('🎉 All database functions are working correctly!');
        
    } catch (error) {
        console.error('❌ Database test failed:', error);
        process.exit(1);
    }
}

// Run the test
testDatabase().then(() => {
    console.log('\n✅ Database test completed successfully');
    process.exit(0);
}).catch(error => {
    console.error('\n❌ Database test failed:', error);
    process.exit(1);
});
