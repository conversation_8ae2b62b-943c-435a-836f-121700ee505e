#!/usr/bin/env node

/**
 * Test script to verify the /recent command functionality
 */

require('dotenv').config();
const db = require('../src/db');

async function testRecentCommand() {
    console.log('🧪 Testing /recent command functionality...\n');
    
    try {
        // Initialize database
        await db.initializeDb();
        console.log('✅ Database initialized\n');
        
        // Test getAllTrades function
        console.log('Testing getAllTrades function...');
        const allTrades = db.getAllTrades();
        console.log(`✅ Found ${allTrades.length} total trades`);
        
        if (allTrades.length > 0) {
            const recentTrades = allTrades.slice(-10).reverse(); // Last 10 trades, newest first
            console.log(`✅ Recent trades (last ${Math.min(10, allTrades.length)}):`);
            
            recentTrades.forEach((trade, index) => {
                const date = new Date(trade.timestamp).toLocaleString();
                console.log(`   ${index + 1}. ${trade.status} - ${trade.from} -> ${trade.quantity} (${trade.memo}) at ${date}`);
            });
        } else {
            console.log('ℹ️  No trades found in database');
        }
        
        console.log('\n🎉 /recent command functionality test completed!');
        
    } catch (error) {
        console.error('❌ Recent command test failed:', error);
        process.exit(1);
    }
}

// Run the test
testRecentCommand().then(() => {
    console.log('\n✅ Recent command test completed successfully');
    process.exit(0);
}).catch(error => {
    console.error('\n❌ Recent command test failed:', error);
    process.exit(1);
});
