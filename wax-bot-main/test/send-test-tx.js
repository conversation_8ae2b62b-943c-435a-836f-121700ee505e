const { Api, JsonRpc } = require('eosjs');
const { JsSignatureProvider } = require('eosjs/dist/eosjs-jssig');
const fetch = require('node-fetch');
const { TextEncoder, TextDecoder } = require('util');

// --- Configuration ---
const privateKey = '5KDodhQoSPs2PqaGvRVYR12W1B7aUSZrqa1M8rirp2uJWHFXLXp'; // waxprofitnew's private key
const sender = 'waxprofitnew';
const receiver = 'blockvestwax';
const amount = '2.00000000 WAX';
const memo = '10003050'; // Use a purely numerical memo as per client requirements
const rpcEndpoint = 'https://wax.greymass.com';

// --- Script ---
const signatureProvider = new JsSignatureProvider([privateKey]);
const rpc = new JsonRpc(rpcEndpoint, { fetch });
const api = new Api({ rpc, signatureProvider, textDecoder: new TextDecoder(), textEncoder: new TextEncoder() });

async function main() {
    try {
        console.log(`Sending ${amount} from ${sender} to ${receiver} with memo: "${memo}"`);

        const result = await api.transact({
            actions: [{
                account: 'eosio.token',
                name: 'transfer',
                authorization: [{
                    actor: sender,
                    permission: 'active',
                }],
                data: {
                    from: sender,
                    to: receiver,
                    quantity: amount,
                    memo: memo,
                },
            }]
        }, {
            blocksBehind: 3,
            expireSeconds: 30,
        });

        console.log('Transaction successful!');
        console.log(`Transaction ID: ${result.transaction_id}`);
        console.log(`View on explorer: https://wax.bloks.io/transaction/${result.transaction_id}`);

    } catch (error) {
        console.error('Error sending transaction:', error.json ? JSON.stringify(error.json, null, 2) : error);
    }
}

main();
