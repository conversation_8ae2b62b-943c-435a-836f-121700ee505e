#!/usr/bin/env node

/**
 * Test script to verify the rules engine is working correctly
 */

require('dotenv').config();
const db = require('../src/db');
const rules = require('../src/rules');

async function testRulesEngine() {
    console.log('🧪 Testing Rules Engine...\n');
    
    try {
        // Initialize database
        await db.initializeDb();
        console.log('✅ Database initialized\n');
        
        // Test 1: High value trade (should be blocked)
        console.log('1. Testing high value trade rule...');
        const highValueTrade = {
            from: 'testuser1',
            to: 'blockvestwax',
            quantity: '150000.00000000 WAX', // Above 135k limit
            memo: 'test-high-value',
            transaction_id: 'test-tx-1',
            timestamp: new Date().toISOString()
        };
        
        const highValueResult = await rules.checkBlockingRules(highValueTrade);
        if (highValueResult) {
            console.log(`✅ High value trade correctly blocked: ${highValueResult}`);
        } else {
            console.log('❌ High value trade should have been blocked!');
        }
        console.log('');
        
        // Test 2: Normal trade (should pass)
        console.log('2. Testing normal trade...');
        const normalTrade = {
            from: 'testuser2',
            to: 'blockvestwax',
            quantity: '100.00000000 WAX',
            memo: 'test-normal',
            transaction_id: 'test-tx-2',
            timestamp: new Date().toISOString()
        };
        
        const normalResult = await rules.checkBlockingRules(normalTrade);
        if (!normalResult) {
            console.log('✅ Normal trade correctly passed rules');
        } else {
            console.log(`❌ Normal trade should have passed: ${normalResult}`);
        }
        console.log('');
        
        // Test 3: Check existing memo with multiple trades
        console.log('3. Testing existing memo with multiple trades...');
        const existingMemoTrade = {
            from: 'waxdididada3',
            to: 'blockvestwax',
            quantity: '50.00000000 WAX',
            memo: '10003050', // This memo has existing trades
            transaction_id: 'test-tx-3',
            timestamp: new Date().toISOString()
        };
        
        const existingResult = await rules.checkBlockingRules(existingMemoTrade);
        console.log(`ℹ️  Existing memo result: ${existingResult || 'Passed rules'}`);
        console.log('');
        
        console.log('🎉 Rules engine test completed!');
        
    } catch (error) {
        console.error('❌ Rules engine test failed:', error);
        process.exit(1);
    }
}

// Run the test
testRulesEngine().then(() => {
    console.log('\n✅ Rules engine test completed successfully');
    process.exit(0);
}).catch(error => {
    console.error('\n❌ Rules engine test failed:', error);
    process.exit(1);
});
