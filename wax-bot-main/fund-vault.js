const { Api, JsonRpc, RpcError } = require('eosjs');
const { JsSignatureProvider } = require('eosjs/dist/eosjs-jssig');
const fetch = require('node-fetch');
const { TextEncoder, TextDecoder } = require('util');

// --- CONFIGURATION ---
const privateKey = 'PVT_K1_2wTsaUYMKTKxAYUMJ7czW4wK1jpZeDMzCAgHVvnuZ3cMGZ8sEm'; // waxblockvest's private key
const sender = 'waxblockvest';
const receiver = 'blockvestwax';
const amount = '20.******** WAX';
const memo = 'Admin top-up for vault account';
const rpcEndpoint = 'https://wax.greymass.com';

// --- SCRIPT ---
const signatureProvider = new JsSignatureProvider([privateKey]);
const rpc = new JsonRpc(rpcEndpoint, { fetch });
const api = new Api({ rpc, signatureProvider, textDecoder: new TextDecoder(), textEncoder: new TextEncoder() });

async function main() {
    try {
        console.log(`Sending ${amount} from ${sender} to ${receiver} with memo: "${memo}"`);
        const result = await api.transact({
            actions: [{
                account: 'eosio.token',
                name: 'transfer',
                authorization: [{
                    actor: sender,
                    permission: 'active',
                }],
                data: {
                    from: sender,
                    to: receiver,
                    quantity: amount,
                    memo: memo,
                },
            }]
        }, {
            blocksBehind: 3,
            expireSeconds: 30,
        });

        console.log('Transaction successful!');
        console.log(`Transaction ID: ${result.transaction_id}`);
        console.log(`View on explorer: https://wax.bloks.io/transaction/${result.transaction_id}`);

    } catch (e) {
        console.error('Transaction failed!');
        if (e instanceof RpcError) {
            console.error(JSON.stringify(e.json, null, 2));
        } else {
            console.error(e);
        }
    }
}

main();
