const { JsonRpc } = require('eosjs');
const fetch = require('node-fetch');

// --- Configuration ---
const accountName = 'blockvestwax';
const rpcEndpoint = 'https://wax.greymass.com';

// --- Script ---
const rpc = new JsonRpc(rpcEndpoint, { fetch });

async function main() {
    try {
        console.log(`Fetching details for account: ${accountName}...\n`);

        // Get account details (CPU, NET, RAM)
        const accountInfo = await rpc.get_account(accountName);
        const { cpu_limit, net_limit, ram_usage, ram_quota } = accountInfo;

        console.log('--- Resources ---');
        console.log(`RAM: ${Math.round(ram_usage / 1024)} KB used / ${Math.round(ram_quota / 1024)} KB total`);
        console.log(`CPU: ${cpu_limit.used / 1000} ms used / ${cpu_limit.max / 1000} ms available`);
        console.log(`NET: ${net_limit.used / 1024} KB used / ${net_limit.max / 1024} KB available\n`);

        // Get WAX balance
        const balance = await rpc.get_currency_balance('eosio.token', accountName, 'WAX');
        console.log('--- Balance ---');
        console.log(`WAX Balance: ${balance[0] || '0.******** WAX'}`);

    } catch (error) {
        console.error('Error fetching account details:', error.json ? JSON.stringify(error.json, null, 2) : error);
    }
}

main();
