// Function to parse WAX quantity string like "10.00000000 WAX"
function parseWaxQuantity(quantity) {
    if (typeof quantity !== 'string') return 0;
    const [amount] = quantity.split(' ');
    return parseFloat(amount);
}

// Function to format a number back into a WAX quantity string
function formatWaxQuantity(amount) {
    return `${amount.toFixed(8)} WAX`;
}

module.exports = {
    parseWaxQuantity,
    formatWaxQuantity,
};
