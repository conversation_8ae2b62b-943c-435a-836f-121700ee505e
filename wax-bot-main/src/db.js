const path = require('path');
const fs = require('fs');
const low = require('lowdb');
const FileAsync = require('lowdb/adapters/FileAsync');

// Ensure database path is relative to project root, not /data
const dbPath = path.resolve(__dirname, '..', process.env.DATABASE_PATH || 'db.json');
console.log(`[DB] Database path resolved to: ${dbPath}`);

// Ensure the directory exists
const dbDir = path.dirname(dbPath);
if (!fs.existsSync(dbDir)) {
    console.log(`[DB] Creating directory: ${dbDir}`);
    fs.mkdirSync(dbDir, { recursive: true });
}

const adapter = new FileAsync(dbPath);
let db;

// --- Initialization ---

async function initializeDb() {
    db = await low(adapter);

    // Updated defaults with the correct payout collections
    const defaults = {
        admins: [],
        trades: [],
        memos: [], // For blacklisting
        pending_payouts: [],
        failed_payouts: [],
        system: { last_processed_block: 0 }
    };
    await db.defaults(defaults).write();

    // Clean up old database structure if it exists
    if (db.has('blacklist').value()) {
        console.log('[DB] Removing deprecated blacklist field from database');
        await db.unset('blacklist').write();
    }
    if (db.has('lastProcessedSeq').value()) {
        console.log('[DB] Removing deprecated lastProcessedSeq field from database');
        await db.unset('lastProcessedSeq').write();
    }
    if (db.has('paused').value()) {
        console.log('[DB] Removing deprecated paused field from database (now handled in memory)');
        await db.unset('paused').write();
    }

    // Merge admin IDs from .env into DB, but only add new ones.
    const envAdmins = (process.env.TELEGRAM_ADMIN_ID || '').split(',').map(id => id.trim()).filter(Boolean);
    if (envAdmins.length > 0) {
        const dbAdmins = (db.get('admins').value() || []).map(String);
        const newAdmins = envAdmins.filter(id => !dbAdmins.includes(id));
        if (newAdmins.length > 0) {
            console.log(`[DB] Adding missing admins from .env: ${newAdmins.join(', ')}`);
            await db.get('admins').push(...newAdmins).write();
        }
    } else if ((db.get('admins').value() || []).length === 0) {
        console.warn('[DB] WARNING: No TELEGRAM_ADMIN_ID found in .env and no admins in DB. Bot will have no admins.');
    }
}

// --- Admin Management ---

const getAdmins = () => {
    if (!db) {
        console.error('[DB ERROR] Database not initialized when getAdmins was called');
        return [];
    }
    return (db.get('admins').value() || []).map(String);
};

const addAdmin = async (adminId) => {
    console.log(`[DB] addAdmin called with: ${adminId}`);
    await db.read(); // Force a re-read to prevent race conditions
    const adminStr = String(adminId);
    const admins = (db.get('admins').value() || []).map(String);
    if (admins.includes(adminStr)) {
        console.log(`[DB] Admin ${adminId} already exists.`);
        return false; // Already exists
    }
    admins.push(adminStr);
    await db.set('admins', admins).write();
    console.log(`[DB] Admin ${adminId} added successfully.`);
    return true; // Added successfully
};

const delAdmin = async (adminId) => {
    console.log(`[DB] delAdmin called with: ${adminId}`);
    await db.read(); // Force a re-read to prevent race conditions
    const adminStr = String(adminId);
    let admins = (db.get('admins').value() || []).map(String);
    if (!admins.includes(adminStr)) {
        console.log(`[DB] Admin ${adminId} not found for deletion.`);
        return false; // Does not exist
    }
    admins = admins.filter(id => id !== adminStr);
    await db.set('admins', admins).write();
    console.log(`[DB] Admin ${adminId} deleted successfully.`);
    return true; // Deleted successfully
};

// --- Memo Blacklisting ---

async function getMemo(memo) {
    if (!db) {
        console.error('[DB ERROR] Database not initialized when getMemo was called');
        return null;
    }
    await db.read(); // Force fresh read to prevent stale cache issues
    return db.get('memos').find({ memo: String(memo) }).value();
}

async function upsertMemo(memo, isBlocked, blockReason = null) {
    console.log(`[DB] upsertMemo called for ${memo}. isBlocked: ${isBlocked}`);
    await db.read(); // Force a re-read to prevent race conditions
    const memoStr = String(memo);
    const memos = db.get('memos').value() || [];
    const memoIndex = memos.findIndex(m => m.memo === memoStr);

    const data = { is_blocked: isBlocked, block_reason: blockReason, updated_at: new Date().toISOString() };

    if (memoIndex > -1) {
        console.log(`[DB] Updating existing memo: ${memoStr}`);
        // Combine old and new data to preserve fields like `created_at`
        const updatedMemo = { ...memos[memoIndex], ...data };
        memos[memoIndex] = updatedMemo;
    } else {
        console.log(`[DB] Inserting new memo: ${memoStr}`);
        data.created_at = new Date().toISOString();
        memos.push({ memo: memoStr, ...data });
    }

    await db.set('memos', memos).write();
    console.log(`[DB] upsertMemo for ${memo} completed successfully.`);
    // Reread the data after write to return the confirmed state
    await db.read();
    const finalMemos = db.get('memos').value();
    return finalMemos.find(m => m.memo === memoStr);
}

function getBlacklistedMemos() {
    if (!db) {
        console.error('[DB ERROR] Database not initialized when getBlacklistedMemos was called');
        return [];
    }
    return db.get('memos').filter({ is_blocked: true }).value() || [];
}

// --- Trade Logging ---

const addTrade = async (trade) => await db.get('trades').push(trade).write();

const getTradesByMemo = (memo) => {
    if (!db) {
        console.error('[DB ERROR] Database not initialized when getTradesByMemo was called');
        return [];
    }
    const memoStr = String(memo);
    return db.get('trades').filter({ memo: memoStr }).value() || [];
};

const getAllTrades = () => {
    if (!db) {
        console.error('[DB ERROR] Database not initialized when getAllTrades was called');
        return [];
    }
    return db.get('trades').value() || [];
};

const getFailedPayouts = () => {
    if (!db) {
        console.error('[DB ERROR] Database not initialized when getFailedPayouts was called');
        return [];
    }
    return db.get('failed_payouts').value() || [];
};

// --- Payout Queue Management ---

async function addPendingPayout(payout) {
    const payoutWithRetry = { ...payout, retries: 0 };
    return await db.get('pending_payouts').push(payoutWithRetry).write();
}

function getPendingPayouts() {
    if (!db) {
        console.error('[DB ERROR] Database not initialized when getPendingPayouts was called');
        return [];
    }
    return db.get('pending_payouts').value() || [];
}

async function removePendingPayout(transaction_id) {
    return await db.get('pending_payouts').remove({ transaction_id }).write();
}

async function updatePayoutRetries(transaction_id) {
    const payout = db.get('pending_payouts').find({ transaction_id });
    if (payout.value()) {
        await payout.assign({ retries: (payout.value().retries || 0) + 1 }).write();
        return payout.value();
    }
    return null;
}

async function moveToFailed(payout) {
    await db.get('pending_payouts').remove({ transaction_id: payout.transaction_id }).write();
    return await db.get('failed_payouts').push(payout).write();
}

async function removeFailedPayout(transaction_id) {
    return await db.get('failed_payouts').remove({ transaction_id }).write();
}

// --- System State ---

const getLastProcessedBlock = () => {
    if (!db) {
        console.error('[DB ERROR] Database not initialized when getLastProcessedBlock was called');
        return 0;
    }
    return db.get('system.last_processed_block').value() || 0;
};

const setLastProcessedBlock = async (blockNum) => await db.set('system.last_processed_block', blockNum).write();

// --- Utility ---

const clearAllData = async () => {
    console.warn('[DB] Admin request to clear all trade, memo, and payout data.');
    await db.set('trades', []).write();
    await db.set('memos', []).write();
    await db.set('pending_payouts', []).write();
    await db.set('failed_payouts', []).write();
    // Admins and system block height are NOT cleared.
    return true;
};

// --- Exports ---

module.exports = {
    initializeDb,
    getAdmins,
    addAdmin,
    delAdmin,
    getMemo,
    upsertMemo,
    getBlacklistedMemos,
    addTrade,
    getTradesByMemo,
    getAllTrades,
    getFailedPayouts,
    addPendingPayout,
    getPendingPayouts,
    removePendingPayout,
    updatePayoutRetries,
    moveToFailed,
    removeFailedPayout,
    getLastProcessedBlock,
    setLastProcessedBlock,
    clearAllData
};
