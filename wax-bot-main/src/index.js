// Load environment variables first
const instanceId = Math.random().toString(36).substring(2);
console.log(`[Instance ${instanceId}] Loading index.js...`);
require('dotenv').config();

// Anti-debugging and runtime protection
function checkIntegrity() {
    const requiredEnvVars = [
        'TELEGRAM_BOT_TOKEN', 'WAX_PAYOUT_PRIVATE_KEY', 'WAX_VAULT_ACCOUNT',
        'TELEGRAM_ADMIN_ID'
    ];

    for (const envVar of requiredEnvVars) {
        if (!process.env[envVar]) {
            console.error(`Missing required environment variable: ${envVar}`);
            process.exit(1);
        }
    }

    // Production deployment verification - DISABLED for direct deployment
    // if (process.env.NODE_ENV === 'production' && !process.env.DEPLOYMENT_KEY) {
    //     console.error('Production deployment requires DEPLOYMENT_KEY');
    //     process.exit(1);
    // }
}

// Run integrity check
checkIntegrity();

// --- Imports ---
const db = require('./db');
const { startBlockchainListener, stopBlockchainListener } = require('./blockchain');
const { initializeCommands } = require('./commands');
const { processPendingPayouts } = require('./payout');
const { bot, startBot, stopBot, notifyAdmins } = require('./telegram');

// --- Global State ---
let payoutProcessorInterval;
let isShuttingDown = false;

// --- Main Application Logic ---

/**
 * Initializes and starts all bot components.
 */
async function start() {
    console.log(`[Instance ${instanceId}] --- Wax Profit Bot Starting ---`);
    await db.initializeDb();

    // Diagnostic logging for admin IDs
    const adminIds = db.getAdmins();
    if (adminIds && adminIds.length > 0) {
        console.log(`[Config] Loaded Admin IDs: ${adminIds.join(', ')}`);
    } else {
        console.warn('[Config] WARNING: No Telegram Admin IDs found. The bot will not be able to send notifications or respond to admin commands.');
    }
    console.log('[OK] Database initialized.');

    initializeCommands(bot, instanceId); // Pass instanceId for debugging
    console.log(`[Instance ${instanceId}] Command registration initiated.`);

    // Start the payout processor loop - more frequent for faster response
    const payoutInterval = 15000; // 15 seconds for faster payout processing
    payoutProcessorInterval = setInterval(processPendingPayouts, payoutInterval);
    console.log(`[OK] Payout processor started (interval: ${payoutInterval / 1000}s).`);

    // Start the blockchain listener
    await startBlockchainListener();
    console.log('[OK] Blockchain listener started.');

    // Start the Telegram bot. This is a blocking call and must be last.
    await startBot();
    console.log('--- Wax Profit Bot Running ---');
}

/**
 * Gracefully shuts down all bot components.
 * @param {string|Error} reason - The signal or error that triggered the shutdown.
 */
async function shutdown(reason) {
    if (isShuttingDown) {
        console.log('Shutdown already in progress.');
        return;
    }
    isShuttingDown = true;

    const isError = reason instanceof Error;
    const reasonMsg = isError ? reason.message : reason;

    console.log(`\n--- Received ${reasonMsg}. Shutting down gracefully... ---`);
    if (isError) {
        console.error(reason); // Log the full error stack
    }

    // Stop the payout processor
    if (payoutProcessorInterval) {
        clearInterval(payoutProcessorInterval);
        console.log('[OK] Payout processor stopped.');
    }

    // Stop the blockchain listener
    stopBlockchainListener();

    // Attempt to notify admins
    try {
        await notifyAdmins(`⚠️ The bot is shutting down (Reason: ${reasonMsg}).`);
        console.log('[OK] Shutdown notification sent to admins.');
    } catch (error) {
        console.error('Failed to send shutdown notification:', error.message);
    }

    // Stop the Telegram bot
    stopBot(reasonMsg);
    console.log('[OK] Telegram bot stopped.');

    // Exit the process
    console.log('--- Shutdown complete. ---');
    process.exit(isError ? 1 : 0);
}

// --- Process Signal & Error Handlers ---

// Graceful shutdown listeners
process.on('SIGINT', () => shutdown('SIGINT'));
process.on('SIGTERM', () => shutdown('SIGTERM'));

// Global error handlers
process.on('uncaughtException', (error) => {
    console.error('[FATAL] Uncaught Exception:');
    shutdown(error);
});
process.on('unhandledRejection', (reason, promise) => {
    console.error('[FATAL] Unhandled Rejection at:', promise);
    // The 'reason' for unhandledRejection can be any value, not just an Error object.
    const error = reason instanceof Error ? reason : new Error(JSON.stringify(reason));
    shutdown(error);
});


// --- Main Execution Block ---
(async () => {
    try {
        await start();
    } catch (error) {
        console.error('[FATAL] A critical error occurred during startup:');
        await shutdown(error);
    }
})();