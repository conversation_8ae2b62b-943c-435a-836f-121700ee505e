const { Telegraf } = require('telegraf');
const fetch = require('cross-fetch'); // For raw API call
const db = require('./db');

if (!process.env.TELEGRAM_BOT_TOKEN) {
    throw new Error('TELEGRAM_BOT_TOKEN must be set in the .env file.');
}

const bot = new Telegraf(process.env.TELEGRAM_BOT_TOKEN);

const isAdmin = (chatId) => {
    if (!chatId) return false;
    const admins = db.getAdmins();
    return admins.includes(String(chatId));
};

const notifyAdmins = async (message, excludeAdminId = null) => {
    const adminIds = db.getAdmins();
    if (!adminIds || adminIds.length === 0) {
        console.warn('[Telegram] No admin IDs configured. Cannot send notification.');
        return;
    }

    const notifications = adminIds
        .filter(adminId => String(adminId) !== String(excludeAdminId))
        .map(async adminId => {
            try {
                await bot.telegram.sendMessage(adminId, message, { parse_mode: 'Markdown' });
                console.log(`[Telegram] Notification sent to admin ${adminId}`);
            } catch (error) {
                console.error(`[Telegram] Failed to send message to admin ${adminId}:`, error.description || error.message);
                // Try sending without markdown if markdown parsing fails
                if (error.description && error.description.includes('parse')) {
                    try {
                        await bot.telegram.sendMessage(adminId, message);
                        console.log(`[Telegram] Notification sent to admin ${adminId} (fallback without markdown)`);
                    } catch (fallbackError) {
                        console.error(`[Telegram] Fallback also failed for admin ${adminId}:`, fallbackError.description || fallbackError.message);
                    }
                }
            }
        });
    await Promise.all(notifications);
};

async function startBot() {
    console.log('[Telegram] Starting bot...');
    try {
        // --- Token Validation (Raw API Call) ---
        console.log('[Telegram] Verifying bot token with raw API call...');
        const token = process.env.TELEGRAM_BOT_TOKEN;
        const response = await fetch(`https://api.telegram.org/bot${token}/getMe`);
        const data = await response.json();

        if (!data.ok) {
            console.error('[FATAL] Telegram API returned an error:');
            console.error(data);
            throw new Error(`Telegram API error: ${data.description}`);
        }

        console.log(`[Telegram] Token validated successfully via raw API call. Bot is running as @${data.result.username}`);

        await notifyAdmins('✅ *Wax Profit Bot has started successfully.*');
        console.log('[Telegram] Launching bot and starting polling...');
        
        // bot.launch() is a blocking call that starts polling.
        // It will only throw an error on critical startup issues (e.g., bad token).
        await bot.launch();
        
        console.log('[Telegram] Polling stopped.'); // This line will only be reached on graceful shutdown.
    } catch (err) {
        console.error('[FATAL] Could not launch bot. This is often due to an invalid TELEGRAM_BOT_TOKEN.');
        console.error(err);
        process.exit(1);
    }
}

// Set up graceful shutdown handlers ONCE.
process.once('SIGINT', () => {
    console.log('[Telegram] SIGINT received. Shutting down...');
    notifyAdmins('⚠️ *Wax Profit Bot is shutting down...* (Signal: SIGINT)');
    bot.stop('SIGINT');
});

process.once('SIGTERM', () => {
    console.log('[Telegram] SIGTERM received. Shutting down...');
    notifyAdmins('⚠️ *Wax Profit Bot is shutting down...* (Signal: SIGTERM)');
    bot.stop('SIGTERM');
});

function stopBot(reason) {
    console.log(`[Telegram] Stopping bot. Reason: ${reason}`);
    bot.stop(reason);
}

module.exports = { bot, isAdmin, notifyAdmins, startBot, stopBot };
