const wax = require('./wax');
const config = require('./config');
const db = require('./db');
const store = require('./store');
const rules = require('./rules');
const payout = require('./payout');
const telegram = require('./telegram');

// Use a state object for more robust position management
const state = {
  lastProcessedPos: null,
  isFetching: false,
  blockchainInterval: null,
  consecutiveEmptyFetches: 0,
  currentInterval: 3000, // Start with 3 seconds
};

async function initializePosition() {
  try {
    let lastPos = db.getLastProcessedBlock();
    if (lastPos > 0) {
      console.log(`[DB] Resuming from last saved position: ${lastPos}`);
      state.lastProcessedPos = lastPos;
    } else {
      console.log('[DB] No position found. Fetching latest action to set starting point...');
      const rpc = wax.getRpc();
      const result = await rpc.history_get_actions(config.waxVaultAccount, -1, -100);
      if (result && result.actions && result.actions.length > 0) {
        const lastAction = result.actions[result.actions.length - 1];
        state.lastProcessedPos = lastAction.account_action_seq + 1;
        await db.setLastProcessedBlock(state.lastProcessedPos);
        console.log(`Set starting position to ${state.lastProcessedPos} and saved to DB.`);
      } else {
        state.lastProcessedPos = 0;
        console.log('No actions found for account. Starting from position 0.');
      }
    }
    console.log(`Listener will now fetch actions from position: ${state.lastProcessedPos}`);
  } catch (error) {
    console.error('[FATAL] Error initializing listener position:', error);
    console.error('Retrying in 5 seconds...');
    setTimeout(initializePosition, 5000);
  }
}

async function getActions() {
  // Prevent multiple concurrent fetches and run only after initialization
  if (state.isFetching || state.lastProcessedPos === null) {
    return;
  }

  state.isFetching = true;

  try {
    const rpc = wax.getRpc();
    if (!rpc) {
      console.error('[Blockchain] RPC client is null, cannot fetch actions');
      return;
    }

    const result = await rpc.history_get_actions(config.waxVaultAccount, state.lastProcessedPos, 100);

    if (!result || !result.actions || result.actions.length === 0) {
      state.consecutiveEmptyFetches++;
      // Adaptive polling: slow down when no activity
      if (state.consecutiveEmptyFetches > 5 && state.currentInterval < 10000) {
        state.currentInterval = Math.min(state.currentInterval + 1000, 10000);
        clearInterval(state.blockchainInterval);
        state.blockchainInterval = setInterval(getActions, state.currentInterval);
        console.log(`[Blockchain] No activity detected. Slowing down polling to ${state.currentInterval}ms`);
      }
      return;
    }

    // Reset interval to fast polling when activity is detected
    if (state.consecutiveEmptyFetches > 0) {
      state.consecutiveEmptyFetches = 0;
      if (state.currentInterval > 3000) {
        state.currentInterval = 3000;
        clearInterval(state.blockchainInterval);
        state.blockchainInterval = setInterval(getActions, state.currentInterval);
        console.log(`[Blockchain] Activity detected. Resuming fast polling at ${state.currentInterval}ms`);
      }
    }

    console.log(`[Blockchain] Found ${result.actions.length} new action(s).`);

    for (const action of result.actions) {
      try {
        // Double-check to prevent re-processing
        if (action.account_action_seq < state.lastProcessedPos) {
          continue;
        }

        if (action.action_trace.receipt.receiver === config.waxVaultAccount && action.action_trace.act.name === 'transfer') {
          const data = action.action_trace.act.data;
          if (action.action_trace.act.account === 'eosio.token' && data.to === config.waxVaultAccount) {
            await processAction(action.action_trace);
          }
        }
        // Update position to the next action to be processed
        state.lastProcessedPos = action.account_action_seq + 1;
        await db.setLastProcessedBlock(state.lastProcessedPos);
      } catch (actionError) {
        console.error(`[Blockchain] Error processing action ${action.account_action_seq}:`, actionError);
        // Continue processing other actions even if one fails
        state.lastProcessedPos = action.account_action_seq + 1;
        await db.setLastProcessedBlock(state.lastProcessedPos);
      }
    }
  } catch (error) {
    console.error('[Blockchain] Error fetching blockchain actions:', error);
    // Notify admins of critical blockchain errors
    await telegram.notifyAdmins(`🚨 *Blockchain Listener Error*\n\nError: \`${error.message}\`\n\nThe bot will continue trying to fetch actions.`);
  } finally {
    state.isFetching = false;
  }
}

async function processAction(actionTrace) {
    try {
        const { act, trx_id, block_time } = actionTrace;
        const { from, to, quantity, memo } = act.data;

        // Log every single incoming transaction for audit purposes
        const tradeData = { from, to, quantity, memo, transaction_id: trx_id, timestamp: block_time };

        console.log(`[Blockchain] Processing transaction: ${trx_id} from ${from} amount ${quantity} memo: "${memo}"`);

        // 1. Check if memo is present (requirement: memo required for profit return)
        if (!memo || memo.trim() === '') {
            console.log(`[No Memo] Transaction from ${from} has no memo. WAX accepted but no payout will be sent.`);
            await db.addTrade({ ...tradeData, status: 'No Memo', block_reason: 'No memo provided - WAX accepted but no payout' });
            await telegram.notifyAdmins(`📥 *WAX Received (No Payout)*\n\nFrom: \`${from}\`\nAmount: \`${quantity}\`\nReason: No memo provided`);
            return;
        }

        // 2. Check if the system is paused
        if (store.isPaused()) {
            console.log(`[Paused] System is paused. Ignoring deposit from ${memo}.`);
            await db.addTrade({ ...tradeData, status: 'Paused', block_reason: 'System is paused' });
            return;
        }

        // 3. Check if memo is already permanently blocked
        const memoRecord = await db.getMemo(memo);
        if (memoRecord && memoRecord.is_blocked) {
            console.log(`[Blocked] Memo ${memo} is already blacklisted. Ignoring deposit.`);
            await db.addTrade({ ...tradeData, status: 'Blocked', block_reason: 'Memo is blacklisted' });
            return;
        }

        // 4. Run the full stateful rule engine
        const blockReason = await rules.checkBlockingRules(tradeData);

        if (blockReason) {
            console.log(`[Blocked] Memo ${memo} was blocked by rules. Reason: ${blockReason}`);
            await db.upsertMemo(memo, true, blockReason); // Permanently block the memo
            await db.addTrade({ ...tradeData, status: 'Blocked', block_reason: blockReason });
            await telegram.notifyAdmins(`🚨 *Memo Automatically Blocked*\n\nMemo: \`${memo}\`\nFrom: \`${from}\`\nAmount: \`${quantity}\`\nReason: ${blockReason}`);
            return;
        }

        // 5. If all rules pass, log as approved and schedule the payout
        console.log(`[Approved] Memo ${memo} passed all checks. Scheduling payout.`);
        await db.addTrade({ ...tradeData, status: 'Approved' });

        // Send payment received notification first
        const receivedMessage = `
💰 *Payment Received*

*From:* \`${from}\`
*Amount:* \`${quantity}\`
*Memo:* \`${memo}\`
*Transaction ID:* \`${trx_id}\`

[View on Waxplorer](https://wax.bloks.io/transaction/${trx_id})

✅ *Status:* Approved - Payout will be processed shortly
        `;
        await telegram.notifyAdmins(receivedMessage);

        // Then schedule the payout (which sends its own notification)
        await payout.schedulePayout(tradeData);

    } catch (error) {
        console.error('[Blockchain] Error in processAction:', error);
        await telegram.notifyAdmins(`🚨 *Error Processing Transaction*\n\nTransaction ID: \`${actionTrace.trx_id}\`\nError: \`${error.message}\`\n\nThis transaction may need manual review.`);
    }
}

async function startBlockchainListener() {

  console.log(`Starting blockchain listener for account: ${config.waxVaultAccount}`);

  await initializePosition();

  state.blockchainInterval = setInterval(getActions, state.currentInterval);
}

function stopBlockchainListener() {
  if (state.blockchainInterval) {
    clearInterval(state.blockchainInterval);
    state.blockchainInterval = null;
    console.log('[OK] Blockchain listener stopped.');
  }
}

// Force immediate blockchain check (useful after admin unblock actions)
function forceBlockchainCheck() {
  console.log('[Blockchain] Forcing immediate blockchain check...');
  // Reset to fast polling
  state.consecutiveEmptyFetches = 0;
  if (state.currentInterval > 3000) {
    state.currentInterval = 3000;
    clearInterval(state.blockchainInterval);
    state.blockchainInterval = setInterval(getActions, state.currentInterval);
    console.log('[Blockchain] Reset to fast polling for immediate response');
  }
  // Trigger immediate check
  setImmediate(getActions);
}

module.exports = { startBlockchainListener, stopBlockchainListener, forceBlockchainCheck };
