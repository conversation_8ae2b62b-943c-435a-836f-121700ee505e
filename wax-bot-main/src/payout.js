const { getApi } = require('./wax');
const config = require('./config');
const { parseWaxQuantity, formatWaxQuantity } = require('./utils');
const db = require('./db');
const telegram = require('./telegram');
const MAX_RETRIES = 5; // Set the retry limit

// Function to get a random delay between 5 and 10 minutes in milliseconds
function getPayoutDelay() {
    const minMinutes = 5;
    const maxMinutes = 10;
    const randomMinutes = Math.random() * (maxMinutes - minMinutes) + minMinutes;
    return Math.floor(randomMinutes * 60 * 1000);
}

/**
 * Schedules a payout by adding it to the persistent database queue.
 */
async function schedulePayout(transfer) {
    const delay = getPayoutDelay();
    const payoutTime = new Date(Date.now() + delay);
    const delayMinutes = (delay / 1000 / 60).toFixed(2);

    const newPayout = {
        transaction_id: transfer.transaction_id,
        payout_time: payoutTime.toISOString(),
        transfer_details: transfer
    };

    await db.addPendingPayout(newPayout);
    console.log(`[Scheduler] Payout for memo ${transfer.memo} queued. Due at: ${payoutTime.toLocaleTimeString()}`);

    const scheduleMessage = `
⏰ *Payout Scheduled*

*Original Deposit From:* \`${transfer.from}\`
*Amount:* \`${transfer.quantity}\`
*Memo:* \`${transfer.memo}\`
*Scheduled in:* \`${delayMinutes} minutes\`

💸 *Payout Amount:* \`${formatWaxQuantity(parseWaxQuantity(transfer.quantity) * 1.015)} WAX\` *(****% profit)*
🎯 *Destination:* \`${config.waxPayoutDestination}\`
    `;
        telegram.notifyAdmins(scheduleMessage);
}

/**
 * Parses a blockchain RPC error to get a user-friendly reason.
 * @param {Error} error The error object from eosjs.
 * @returns {string} A user-friendly error reason.
 */
function getPayoutErrorReason(error) {
    if (error.message.includes('overdrawn balance')) {
        return 'The vault account has an overdrawn balance (insufficient WAX). Please deposit more WAX.';
    }
    if (error.message.includes('cpu bandwidth')) {
        return 'The vault account has insufficient CPU resources. Please stake more WAX for CPU.';
    }
    if (error.message.includes('net bandwidth')) {
        return 'The vault account has insufficient NET resources. Please stake more WAX for NET.';
    }
    if (error.message.includes('ram usage')) {
        return 'The vault account has insufficient RAM. Please buy more RAM.';
    }
    return error.message; // Fallback to the raw error message
}

/**
 * Sends the actual payout transaction to the blockchain.
 * @returns {object} An object with success status and a reason if failed.
 */
async function sendPayout(transfer) {
    const api = getApi();
    try {
        const incomingAmount = parseWaxQuantity(transfer.quantity);
        const profit = incomingAmount * 0.015;
        const payoutAmount = incomingAmount + profit;

        const action = {
            account: 'eosio.token',
            name: 'transfer',
            authorization: [{
                actor: config.waxPayoutAccount,
                permission: 'active',
            }],
            data: {
                from: config.waxPayoutAccount,
                to: config.waxPayoutDestination,
                quantity: formatWaxQuantity(payoutAmount),
                memo: transfer.memo,
            },
        };

        const result = await api.transact({ actions: [action] }, {
            blocksBehind: 3,
            expireSeconds: 30,
        });

        return { success: true, transaction_id: result.transaction_id };

    } catch (error) {
        console.error(`[Processor] Error sending payout for memo ${transfer.memo}:`, error.message);
        const reason = getPayoutErrorReason(error);
        return { success: false, reason: reason };
    }
}

/**
 * The main processor loop. Checks the DB for due payouts and processes them.
 */
async function processPendingPayouts() {
    const pending = db.getPendingPayouts();
    if (!pending || pending.length === 0) return;

    const now = new Date();
    const duePayouts = pending.filter(p => new Date(p.payout_time) <= now);

    if (duePayouts.length > 0) {
        console.log(`[Processor] Found ${duePayouts.length} due payout(s).`);
    }

    for (const payout of duePayouts) {
        console.log(`[Processor] Processing payout for memo: ${payout.transfer_details.memo} (Attempt ${(payout.retries || 0) + 1})`);
        const result = await sendPayout(payout.transfer_details);

        if (result.success) {
            console.log(`[Processor] Payout successful for memo ${payout.transfer_details.memo}. Transaction ID: ${result.transaction_id}`);
            const successMessage = `
💸 *Payout Sent!*

*Original Deposit From:* \`${payout.transfer_details.from}\`
*Payout To:* \`${config.waxPayoutDestination}\`
*Original Memo:* \`${payout.transfer_details.memo}\`

[View on Waxplorer](https://wax.bloks.io/transaction/${result.transaction_id})
            `;
                        telegram.notifyAdmins(successMessage);
            await db.removePendingPayout(payout.transaction_id);
            console.log(`[Processor] Removed processed payout for memo: ${payout.transfer_details.memo} from queue.`);
        } else {
            // Payout failed, implement retry logic
            const updatedPayout = await db.updatePayoutRetries(payout.transaction_id);
            
            if (updatedPayout && updatedPayout.retries >= MAX_RETRIES) {
                console.error(`[Processor] Payout for memo ${payout.transfer_details.memo} has failed ${MAX_RETRIES} times. Moving to failed queue.`);
                await db.moveToFailed(updatedPayout);
                const finalFailMessage = `
🚨 *Payout Permanently Failed!*

A payout could not be sent after ${MAX_RETRIES} attempts and requires manual intervention.

*Memo:* \`${payout.transfer_details.memo}\`
*Reason:* \`${result.reason}\`

This payout will not be retried automatically.
                `;
                                telegram.notifyAdmins(finalFailMessage);
            } else if (updatedPayout && updatedPayout.retries === 1) {
                // Only send a notification on the first failure to avoid spam
                const firstFailMessage = `
⚠️ *Payout Failed (Attempt 1)*

An error occurred while trying to send a payout for memo \`${payout.transfer_details.memo}\`. The bot will retry automatically.

*Reason:* \`${result.reason}\`
                `;
                                telegram.notifyAdmins(firstFailMessage);
                console.log(`[Processor] Payout for memo ${payout.transfer_details.memo} failed. Will retry. (Attempt ${updatedPayout.retries})`);
            } else {
                 console.log(`[Processor] Payout for memo ${payout.transfer_details.memo} failed. Will retry. (Attempt ${updatedPayout ? updatedPayout.retries : 'unknown'})`);
            }
        }
    }
}

/**
 * Manually retry a failed payout for a specific memo
 * @param {string} memo - The memo to retry payout for
 * @returns {object} Result of the retry operation
 */
async function retryPayoutForMemo(memo) {
    const failedPayouts = db.getFailedPayouts();
    const memoFailedPayouts = failedPayouts.filter(p => p.transfer_details.memo === memo);

    if (memoFailedPayouts.length === 0) {
        return { success: false, reason: `No failed payouts found for memo: ${memo}` };
    }

    // Get the most recent failed payout for this memo
    const latestFailedPayout = memoFailedPayouts.sort((a, b) =>
        new Date(b.transfer_details.timestamp) - new Date(a.transfer_details.timestamp)
    )[0];

    console.log(`[Manual Retry] Attempting to retry payout for memo: ${memo}`);

    // Try to send the payout
    const result = await sendPayout(latestFailedPayout.transfer_details);

    if (result.success) {
        // Remove from failed payouts since it succeeded
        await db.removeFailedPayout(latestFailedPayout.transaction_id);
        console.log(`[Manual Retry] Successfully retried payout for memo: ${memo}`);

        const successMessage = `
💸 *Manual Payout Retry Successful!*

*Original Deposit From:* \`${latestFailedPayout.transfer_details.from}\`
*Payout To:* \`${config.waxPayoutDestination}\`
*Memo:* \`${memo}\`

[View on Waxplorer](https://wax.bloks.io/transaction/${result.transaction_id})
        `;
        telegram.notifyAdmins(successMessage);

        return {
            success: true,
            transaction_id: result.transaction_id,
            message: `Payout successfully sent for memo: ${memo}`
        };
    } else {
        console.log(`[Manual Retry] Failed to retry payout for memo: ${memo}. Reason: ${result.reason}`);
        return {
            success: false,
            reason: result.reason,
            message: `Failed to retry payout for memo: ${memo}. Reason: ${result.reason}`
        };
    }
}

module.exports = { schedulePayout, processPendingPayouts, retryPayoutForMemo };