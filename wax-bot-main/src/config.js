module.exports = {
  telegramBotToken: process.env.TELEGRAM_BOT_TOKEN,
  telegramAdminIds: process.env.TELEGRAM_ADMIN_ID ? process.env.TELEGRAM_ADMIN_ID.split(',').map(id => id.trim()) : [],
  waxVaultAccount: process.env.WAX_VAULT_ACCOUNT,
  waxPayoutAccount: process.env.WAX_PAYOUT_ACCOUNT,
  waxPayoutPrivateKey: process.env.WAX_PAYOUT_PRIVATE_KEY,
  waxApiEndpoint: process.env.WAX_API_ENDPOINT,
  waxPayoutDestination: process.env.WAX_PAYOUT_DESTINATION || 'bybitwaxonly', // Make payout destination configurable
};
