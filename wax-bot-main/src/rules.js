const db = require('./db');
const { parseWaxQuantity } = require('./utils');
const moment = require('moment');

// ============================================================================
// TRADING RULES CONFIGURATION
// ============================================================================
// These constants define the limits for all trading rules

const HIGH_VALUE_THRESHOLD = 135000;  // Rule #2: Maximum WAX per single transaction
const DAILY_TRADE_LIMIT = 10;         // Rule #1: Maximum trades per day

// ============================================================================
// MAIN RULES ENGINE CLASS
// ============================================================================
// This class processes all trading rules for incoming WAX transactions

class StatefulRulesEngine {
    constructor(memo, newTrade) {
        this.memo = memo;           // The memo from the transaction
        this.newTrade = newTrade;   // The current transaction being processed
        this.allTrades = [];        // All trades for this memo (filtered by admin unblock)
        this.reason = null;         // Reason for blocking (if any)
    }

    // ========================================================================
    // MAIN RULE CHECKER - Runs all rules in order
    // ========================================================================
    // This method runs all trading rules and returns block reason if any rule fails
    async check() {
        const historicalTrades = db.getTradesByMemo(this.memo);

        // ADMIN UNBLOCK PROTECTION: Filter trades if memo was recently unblocked
        const filteredTrades = await this.getFilteredTradesAfterUnblock(historicalTrades);
        this.allTrades = [...filteredTrades, this.newTrade];

        // Run all rules in order - if any rule fails, return the reason
        if (this.isHighValueTrade()) return this.reason;        // Rule #2: High Value
        if (await this.isOverDailyTradeLimit()) return this.reason; // Rule #1: Daily Limit
        if (this.isDay5RuleBroken()) return this.reason;        // Rule #3: Day 5 Rule
        if (this.isDay4RuleBroken()) return this.reason;        // Rule #4: Day 4 Pattern

        return null; // All rules passed - transaction approved
    }

    // ========================================================================
    // RULE #2: HIGH VALUE TRADE PROTECTION
    // ========================================================================
    // Blocks single transactions that are too large (≥ 135,000 WAX)
    // Purpose: Prevents suspicious large transactions
    // Search terms: "Rule 2", "High Value", "Single Trade", "135000"
    isHighValueTrade() {
        const amount = parseWaxQuantity(this.newTrade.quantity);
        if (amount >= HIGH_VALUE_THRESHOLD) {
            this.reason = `Rule #2: Single trade of ${amount} WAX exceeds the ${HIGH_VALUE_THRESHOLD} WAX limit.`;
            return true;
        }
        return false;
    }

    // ========================================================================
    // ADMIN UNBLOCK PROTECTION SYSTEM
    // ========================================================================
    // When admin unblocks a memo, this system "resets" the trading history
    // for 24 hours, allowing immediate trading without rule restrictions
    // Search terms: "Admin Unblock", "Reset History", "24 hours", "Filtered Trades"
    async getFilteredTradesAfterUnblock(historicalTrades) {
        // Get memo record to check if it was recently unblocked by admin
        const memoRecord = await db.getMemo(this.memo);

        if (memoRecord && memoRecord.updated_at && !memoRecord.is_blocked) {
            const lastUpdate = moment.utc(memoRecord.updated_at);
            const newTradeTime = moment.utc(this.newTrade.timestamp);

            // If memo was unblocked recently (within last 24 hours), only use trades after unblock
            if (newTradeTime.diff(lastUpdate, 'hours') <= 24) {
                const filteredTrades = historicalTrades.filter(t =>
                    moment.utc(t.timestamp).isAfter(lastUpdate)
                );
                console.log(`[Rules] Memo ${this.memo} was unblocked at ${lastUpdate.format()}. Using ${filteredTrades.length} trades after unblock (was ${historicalTrades.length}).`);
                return filteredTrades; // Return only trades AFTER admin unblock
            }
        }

        return historicalTrades; // Normal behavior - use all historical trades
    }

    // ========================================================================
    // RULE #1: DAILY TRADE LIMIT
    // ========================================================================
    // Blocks memos that exceed 10 trades in a single day
    // Purpose: Prevents high-frequency trading abuse
    // Search terms: "Rule 1", "Daily Limit", "10 trades", "Same Day"
    async isOverDailyTradeLimit() {
        const today = moment.utc(this.newTrade.timestamp);
        const todaysTrades = this.allTrades.filter(t => moment.utc(t.timestamp).isSame(today, 'day'));

        if (todaysTrades.length > DAILY_TRADE_LIMIT) {
            this.reason = `Rule #1: Exceeded daily trade limit of ${DAILY_TRADE_LIMIT} trades.`;
            return true;
        }
        return false;
    }

    // ========================================================================
    // HELPER FUNCTION: TRADE ACTIVITY ANALYZER
    // ========================================================================
    // Analyzes trading patterns by day for Rules #3 and #4
    // Returns: Map with date as key, {count, volume} as value
    // Search terms: "Trade Activity", "Daily Analysis", "Helper Function"
    getTradeActivityByDay() {
        const activity = new Map();
        for (const trade of this.allTrades) {
            const day = moment.utc(trade.timestamp).format('YYYY-MM-DD');
            if (!activity.has(day)) {
                activity.set(day, { count: 0, volume: 0 });
            }
            const dayData = activity.get(day);
            dayData.count += 1;                                    // Count of trades on this day
            dayData.volume += parseWaxQuantity(trade.quantity);    // Total WAX volume on this day
        }
        return activity;
    }

    // ========================================================================
    // RULE #3: DAY 5 RULE (5th Unique Trading Day Limit)
    // ========================================================================
    // Blocks memos that exceed 5 trades on their 5th unique trading day
    // Purpose: Prevents abuse after establishing trading pattern
    // Search terms: "Rule 3", "Day 5", "5th Day", "Unique Trading Days", "5 trades"
    isDay5RuleBroken() {
        const tradeActivity = this.getTradeActivityByDay();
        const uniqueTradeDays = Array.from(tradeActivity.keys()).sort();

        // Check if this memo has reached 5 unique trading days
        if (uniqueTradeDays.length >= 5) {
            const fifthDay = uniqueTradeDays[4];  // 5th day (0-indexed array)
            const today = moment.utc(this.newTrade.timestamp).format('YYYY-MM-DD');

            // If today IS the 5th unique trading day, check trade count limit
            if (today === fifthDay && tradeActivity.get(fifthDay).count > 5) {
                this.reason = `Rule #3: Exceeded 5 trades on the 5th unique day of trading (blocked on 6th trade).`;
                return true;
            }
        }
        return false;
    }

    // ========================================================================
    // RULE #4: DAY 4 PATTERN RULE (Low-Volume Pattern Detection)
    // ========================================================================
    // Blocks memos that exceed 5 trades on Day 4 after 3 consecutive low-volume days
    // Purpose: Prevents pattern abuse after low-activity periods
    // Search terms: "Rule 4", "Day 4", "Pattern", "Low Volume", "3 consecutive days"
    isDay4RuleBroken() {
        const tradeActivity = this.getTradeActivityByDay();
        const uniqueTradeDays = Array.from(tradeActivity.keys()).sort();

        // Rule requires at least 4 unique trading days for the pattern to emerge
        if (uniqueTradeDays.length < 4) {
            return false;
        }

        // Find the index of the current trading day
        const today = moment.utc(this.newTrade.timestamp).format('YYYY-MM-DD');
        const todayIndex = uniqueTradeDays.indexOf(today);

        // If today is one of the first three trading days, this rule doesn't apply
        if (todayIndex < 3) {
            return false;
        }

        // Get the three unique trading days immediately before the current one
        const previousThreeDays = uniqueTradeDays.slice(todayIndex - 3, todayIndex);

        // Check if all three of those days were low-volume (< 135,000 WAX each)
        const arePreviousDaysLowVolume = previousThreeDays.every(day => {
            return tradeActivity.get(day).volume < HIGH_VALUE_THRESHOLD;
        });

        if (arePreviousDaysLowVolume) {
            // PATTERN DETECTED: 3 consecutive low-volume days
            // Allow 5 trades → Block on 6th trade (as per contract specification)
            if (tradeActivity.get(today).count >= 6) {
                this.reason = `Rule #4: Exceeded 5 trades on Day 4 after 3 consecutive low-volume days (blocked on 6th trade).`;
                return true;
            }
        }
        return false;
    }
}

// ============================================================================
// MAIN EXPORT FUNCTION
// ============================================================================
// This is the main function called by the bot to check if a transaction should be blocked
// Search terms: "Main Function", "Export", "Check Rules"
async function checkBlockingRules(transfer) {
    const engine = new StatefulRulesEngine(transfer.memo, transfer);
    return await engine.check();
}

module.exports = { checkBlockingRules };

// ============================================================================
// RULES SUMMARY FOR QUICK REFERENCE
// ============================================================================
// Rule #1: Daily Limit      - Max 10 trades per day
// Rule #2: High Value       - Max 135,000 WAX per transaction
// Rule #3: Day 5 Rule       - Max 5 trades on 5th unique trading day
// Rule #4: Day 4 Pattern    - Max 5 trades on Day 4 after 3 low-volume days
// Admin Unblock Protection  - Resets rules for 24 hours after admin unblock
