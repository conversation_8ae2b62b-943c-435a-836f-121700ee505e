const { Api, JsonRpc } = require('eosjs');
const { JsSignatureProvider } = require('eosjs/dist/eosjs-jssig');
const fetch = require('cross-fetch');
const { TextEncoder, TextDecoder } = require('util');
const config = require('./config');

// Ensure the private key is provided
if (!config.waxPayoutPrivateKey) {
  console.error('Error: WAX_PAYOUT_PRIVATE_KEY is not set in the .env file.');
  process.exit(1);
}

const signatureProvider = new JsSignatureProvider([config.waxPayoutPrivateKey]);

let rpc = null;
let api = null;

// Factory function to create the RPC client on demand
function getRpc() {
    if (!rpc) {
        console.log(`[WAX] Initializing RPC client with endpoint: ${config.waxApiEndpoint}`);
        try {
            rpc = new JsonRpc(config.waxApiEndpoint, { fetch });
            console.log('[WAX] RPC client initialized successfully.');
        } catch (e) {
            console.error('[FATAL] Error creating JsonRpc client:', e);
            rpc = null;
        }
    }
    return rpc;
}

// Factory function to create the API client on demand
function getApi() {
    if (!api) {
        console.log('[WAX] Initializing API client...');
        const rpcClient = getRpc();
        if (!rpcClient) {
            console.error('[FATAL] getRpc() returned null, cannot create API client.');
            return null;
        }
        try {
            api = new Api({
                rpc: rpcClient,
                signatureProvider,
                textDecoder: new TextDecoder(),
                textEncoder: new TextEncoder(),
            });
            console.log('[WAX] API client initialized successfully.');
        } catch (e) {
            console.error('[FATAL] Error creating API client:', e);
            api = null;
        }
    }
    return api;
}

async function getAccountResources() {
    const rpc = getRpc();
    try {
        const account = await rpc.get_account(config.waxPayoutAccount);
        const waxBalance = account.core_liquid_balance || '0.******** WAX';
        const ramUsage = `${((account.ram_usage / account.ram_quota) * 100).toFixed(2)}% (${(account.ram_usage / 1024).toFixed(2)} KB / ${(account.ram_quota / 1024).toFixed(2)} KB)`;
        const cpuStaked = account.total_resources.cpu_weight;
        const netStaked = account.total_resources.net_weight;

        return {
            waxBalance,
            ramUsage,
            cpuStaked,
            netStaked,
        };
    } catch (error) {
        console.error(`Error fetching resources for ${config.waxPayoutAccount}:`, error);
        return null;
    }
}

module.exports = { getRpc, getApi, getAccountResources };
