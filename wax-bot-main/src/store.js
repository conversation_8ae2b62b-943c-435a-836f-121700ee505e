const state = {
  paused: false,
};

function getStatus() {
    return {
        paused: state.paused,
    };
}

function setPaused(isPaused) {
    state.paused = isPaused;
    console.log(`System paused state set to: ${isPaused}`);
}

function isPaused() {
    return state.paused;
}

function clearAll() {
    state.paused = false;
    console.log('In-memory store (pause state) has been cleared.');
}

module.exports = {
  getStatus,
  setPaused,
  isPaused,
  clearAll,
};
