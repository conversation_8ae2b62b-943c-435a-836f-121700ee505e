const db = require('./db');
const store = require('./store');
const { getAccountResources } = require('./wax');
const { retryPayoutForMemo } = require('./payout');
const { forceBlockchainCheck } = require('./blockchain');
const config = require('./config');
const { formatISO } = require('date-fns');
const { isAdmin, notifyAdmins } = require('./telegram');

function initializeCommands(bot, instanceId) {

    const helpMessage =
        '*Wax Profit Bot Admin Commands*\n\n' +
        '*Bot Control:*\n' +
        '`/status` - View current system status, pause state, and blacklist.\n' +
        '`/pause` - Pause all automatic payouts.\n' +
        '`/resume` - Resume automatic payouts.\n' +
        '`/resources` - Check vault account balance and resources.\n\n' +
        '*Memo Management:*\n' +
        '`/blist <memo>` - Permanently block a memo.\n' +
        '`/unblist <memo>` - Un-block a memo.\n' +
        '`/listblocked` - List all blocked memos.\n\n' +
        '*Admin Management:*\n' +
        '`/addadmin <id>` - Add a new admin by Telegram ID.\n' +
        '`/deladmin <id>` - Remove an admin by Telegram ID.\n' +
        '`/listadmins` - List all current admin IDs.\n\n' +
        '*Data Management:*\n' +
        '`/clearall` - Wipe all trade history and the blacklist. *Use with caution!*\n' +
        '`/recent` - Show recent transactions.\n' +
        '`/pending` - Show pending and failed payouts.\n' +
        '`/retrypayout <memo>` - Retry a failed payout for a specific memo.';

    // --- Middleware for Admin-Only Commands ---
    const adminOnly = (ctx, next) => {
        const userId = ctx.from.id;
        console.log(`[Middleware] adminOnly checking access for user ID: ${userId}`);
        const isUserAdmin = isAdmin(userId);
        console.log(`[Middleware] isAdmin check for ${userId} returned: ${isUserAdmin}`);
        if (isUserAdmin) {
            console.log(`[Middleware] Access GRANTED for ${userId}. Proceeding to command.`);
            return next();
        }
        console.log(`[Middleware] Access DENIED for ${userId}. Replying to user.`);
        return ctx.reply('⛔️ Access Denied. You are not an admin.');
    };

    // --- /start & /help ---
    bot.start(adminOnly, (ctx) => ctx.reply(helpMessage, { parse_mode: 'Markdown' }));
    bot.help(adminOnly, (ctx) => ctx.reply(helpMessage, { parse_mode: 'Markdown' }));

    // --- /ping (Public Test Command) ---
    bot.command('ping', (ctx) => {
        console.log(`[Cmd] Received /ping command from ${ctx.from.id}`);
        ctx.reply('🏓 Pong!');
    });

    // --- /status ---
    bot.command('status', adminOnly, async (ctx) => {
        try {
            const status = store.getStatus();
            const blacklist = await db.getBlacklistedMemos();
            const pendingPayouts = db.getPendingPayouts();
            const failedPayouts = db.getFailedPayouts();

            let message = `*System Status*\n\n`;
            message += `*System Paused:* \`${status.paused}\`\n`;
            message += `*Vault Account:* \`${config.waxVaultAccount}\`\n`;
            message += `*Payout Account:* \`${config.waxPayoutAccount}\`\n`;
            message += `*Payout Destination:* \`${config.waxPayoutDestination}\`\n\n`;

            message += `*Queue Status:*\n`;
            message += `*Pending Payouts:* \`${pendingPayouts.length}\`\n`;
            message += `*Failed Payouts:* \`${failedPayouts.length}\`\n`;
            message += `*Blocked Memos:* \`${blacklist.length}\`\n\n`;

            if (blacklist.length > 0) {
                message += `*Blocked Memos List:*\n`;
                blacklist.slice(0, 10).forEach(record => { // Limit to first 10 to avoid message length issues
                    message += `- \`${record.memo}\`\n`;
                });
                if (blacklist.length > 10) {
                    message += `... and ${blacklist.length - 10} more\n`;
                }
            } else {
                message += `*No memos are currently blocked.*`;
            }

            ctx.reply(message, { parse_mode: 'Markdown' });
        } catch (error) {
            console.error('[ERROR] Failed to get status:', error);
            ctx.reply('❌ Failed to get system status. Check the logs.');
        }
    });

    // Blacklist a memo
    bot.command('blist', adminOnly, async (ctx) => {
        console.log(`[Cmd] Received /blist command from ${ctx.from.id}`);
        const memo = ctx.message.text.split(' ')[1];
        if (!memo) return ctx.reply('Usage: /blist <memo>');

        try {
            console.log(`[Cmd] Calling db.upsertMemo for ${memo} with isBlocked=true`);
            await db.upsertMemo(memo, true, `Manually blocked by admin ${ctx.from.id}`);
            console.log(`[Cmd] Successfully blocked memo ${memo}`);
            ctx.reply(`✅ Memo \`${memo}\` has been permanently blocked.`);
            await notifyAdmins(`🚨 Admin ${ctx.from.username} (@${ctx.from.id}) manually blocked memo: \`${memo}\``, ctx.from.id);
        } catch (error) {
            console.error(`[ERROR] Failed to blist memo ${memo}:`, error);
            ctx.reply(`❌ Failed to block memo \`${memo}\`. Check the logs for details.`);
        }
    });

    // Un-blacklist a memo
    bot.command('unblist', adminOnly, async (ctx) => {
        console.log(`[Cmd] Received /unblist command from ${ctx.from.id}`);
        const memo = ctx.message.text.split(' ')[1];
        if (!memo) return ctx.reply('Usage: /unblist <memo>');

        try {
            console.log(`[Cmd] Calling db.upsertMemo for ${memo} with isBlocked=false`);
            await db.upsertMemo(memo, false, null);
            console.log(`[Cmd] Successfully unblocked memo ${memo}`);

            // Force immediate blockchain check for faster response
            forceBlockchainCheck();
            console.log(`[Cmd] Triggered immediate blockchain check after unblocking ${memo}`);

            ctx.reply(`✅ Memo \`${memo}\` has been unblocked. The system will check for new transactions immediately.`);
            await notifyAdmins(`✅ Admin ${ctx.from.username} (@${ctx.from.id}) unblocked memo: \`${memo}\``, ctx.from.id);
        } catch (error) {
            console.error(`[ERROR] Failed to unblist memo ${memo}:`, error);
            ctx.reply(`❌ Failed to unblock memo \`${memo}\`. Check the logs for details.`);
        }
    });

    // List all blocked memos
    bot.command('listblocked', adminOnly, async (ctx) => {
        console.log(`[Cmd] Received /listblocked command from ${ctx.from.id}`);
        try {
            const blockedMemos = await db.getBlacklistedMemos();
            let message = '*Currently Blocked Memos:*\n';
            if (blockedMemos && blockedMemos.length > 0) {
                blockedMemos.forEach(record => {
                    message += `- \`${record.memo}\` (Reason: ${record.block_reason || 'N/A'})\n`;
                });
            } else {
                message = '✅ No memos are currently blocked.';
            }
            ctx.reply(message, { parse_mode: 'Markdown' });
        } catch (error) {
            console.error('[ERROR] Failed to list blocked memos:', error);
            ctx.reply('❌ Failed to list blocked memos. Check the logs.');
        }
    });

    // --- /pause ---
    bot.command('pause', adminOnly, async (ctx) => {
        store.setPaused(true);
        await notifyAdmins(`🚨 *System Paused by ${ctx.from.username}.* All automatic payouts are now disabled.`);
        ctx.reply('✅ System is now PAUSED.');
    });

    // --- /resume ---
    bot.command('resume', adminOnly, async (ctx) => {
        store.setPaused(false);
        await notifyAdmins(`✅ *System Resumed by ${ctx.from.username}.* Automatic payouts are now enabled.`);
        ctx.reply('✅ System is now RESUMED.');
    });

    // --- /clearall ---
    bot.command('clearall', adminOnly, (ctx) => {
        ctx.reply(
            '⚠️ *Are you sure you want to wipe all data?*\n' +
            'This will delete all trade history, the memo blacklist, and reset the blockchain listener position. This action cannot be undone.\n\n' +
            'Type `/confirmclear` to proceed.',
            { parse_mode: 'Markdown' }
        );
    });

    bot.command('confirmclear', adminOnly, async (ctx) => {
        try {
            await db.clearAllData();
            const message = '✅ All transactional data has been wiped. The bot will restart from the latest blockchain action.';
            ctx.reply(message);
            await notifyAdmins(`🧹 Admin ${ctx.from.username} (@${ctx.from.id}) wiped all data.`);
        } catch (error) {
            console.error('Error during /confirmclear:', error);
            ctx.reply('❌ An unexpected error occurred while clearing the data.');
        }
    });

    // --- Admin Management ---
    bot.command('addadmin', adminOnly, async (ctx) => {
        console.log(`[Cmd] Received /addadmin command from ${ctx.from.id}`);
        const adminId = ctx.message.text.split(' ')[1]?.trim();
        if (!adminId || !/^\d+$/.test(adminId)) {
            return ctx.reply('⚠️ Invalid or missing Telegram ID. Usage: /addadmin <id>');
        }

        try {
            console.log(`[Cmd] Calling db.addAdmin with: ${adminId}`);
            const success = await db.addAdmin(adminId);
            if (success) {
                ctx.reply(`✅ Admin added: ${adminId}`);
                await notifyAdmins(`➕ New admin added by ${ctx.from.username}: ${adminId}`, ctx.from.id);
            } else {
                ctx.reply(`⚠️ Admin ${adminId} already exists.`);
            }
        } catch (error) {
            console.error(`[ERROR] Failed to add admin ${adminId}:`, error);
            ctx.reply(`❌ Failed to add admin ${adminId}. Check the logs.`);
        }
    });

    bot.command('deladmin', adminOnly, async (ctx) => {
        console.log(`[Cmd] Received /deladmin command from ${ctx.from.id}`);
        const adminId = ctx.message.text.split(' ')[1]?.trim();
        if (!adminId || !/^\d+$/.test(adminId)) {
            return ctx.reply('⚠️ Invalid or missing Telegram ID. Usage: /deladmin <id>');
        }

        try {
            console.log(`[Cmd] Calling db.delAdmin with: ${adminId}`);
            const success = await db.delAdmin(adminId);
            if (success) {
                ctx.reply(`✅ Admin removed: ${adminId}`);
                await notifyAdmins(`🚫 Admin removed by ${ctx.from.username}: ${adminId}`, ctx.from.id);
            } else {
                ctx.reply(`⚠️ Admin ${adminId} not found.`);
            }
        } catch (error) {
            console.error(`[ERROR] Failed to delete admin ${adminId}:`, error);
            ctx.reply(`❌ Failed to delete admin ${adminId}. Check the logs.`);
        }
    });

    bot.command('listadmins', adminOnly, async (ctx) => {
        console.log(`[Cmd] Received /listadmins command from ${ctx.from.id}`);
        try {
            const admins = db.getAdmins();
            let message = '*Current Admins:*\n';
            if (admins && admins.length > 0) {
                admins.forEach(id => {
                    message += `- \`${id}\`\n`;
                });
            } else {
                message = 'No admins are currently configured.';
            }
            ctx.reply(message, { parse_mode: 'Markdown' });
        } catch (error) {
            console.error('[ERROR] Failed to list admins:', error);
            ctx.reply('❌ Failed to list admins. Check the logs.');
        }
    });

    bot.command('resources', adminOnly, async (ctx) => {
        try {
            await ctx.reply('⏳ Fetching account resources...');
            const resources = await getAccountResources();
            if (resources) {
                const message = `
*Vault Account Resources* (\`${config.waxPayoutAccount}\`)

*Balance:* \`${resources.waxBalance}\`
*RAM Usage:* \`${resources.ramUsage}\`
*CPU Staked:* \`${resources.cpuStaked}\`
*NET Staked:* \`${resources.netStaked}\`
                `;
                ctx.replyWithMarkdown(message);
            } else {
                ctx.reply('❌ Failed to fetch account resources. Check the bot logs.');
            }
        } catch (error) {
            console.error('Error in /resources command:', error);
            ctx.reply('❌ An error occurred while fetching resources.');
        }
    });

    // This command is potentially dangerous if a trade was legitimate but failed for a temporary reason.
    // It's better to have a manual review process.
    // --- /recent ---
    bot.command('recent', adminOnly, async (ctx) => {
        try {
            // We need to add a function to get all trades from db.js
            const allTrades = db.getAllTrades();
            const recentTrades = allTrades.slice(-10).reverse(); // Last 10 trades, newest first

            if (recentTrades.length === 0) {
                return ctx.reply('No recent transactions found.');
            }

            let message = '*Recent Transactions (Last 10):*\n\n';
            recentTrades.forEach((trade, index) => {
                const date = new Date(trade.timestamp).toLocaleString();
                message += `${index + 1}. *${trade.status}*\n`;
                message += `   From: \`${trade.from}\`\n`;
                message += `   Amount: \`${trade.quantity}\`\n`;
                message += `   Memo: \`${trade.memo}\`\n`;
                message += `   Time: \`${date}\`\n`;
                if (trade.block_reason) {
                    message += `   Reason: \`${trade.block_reason}\`\n`;
                }
                message += '\n';
            });

            ctx.reply(message, { parse_mode: 'Markdown' });
        } catch (error) {
            console.error('[ERROR] Failed to get recent transactions:', error);
            ctx.reply('❌ Failed to get recent transactions. Check the logs.');
        }
    });

    // --- /pending ---
    bot.command('pending', adminOnly, async (ctx) => {
        console.log(`[Cmd] Received /pending command from ${ctx.from.id}`);
        try {
            const pendingPayouts = db.getPendingPayouts();
            const failedPayouts = db.getFailedPayouts();

            let message = '*Pending & Failed Payouts*\n\n';

            // Show pending payouts
            if (pendingPayouts && pendingPayouts.length > 0) {
                message += `*🕐 Pending Payouts (${pendingPayouts.length}):*\n`;
                pendingPayouts.slice(0, 5).forEach((payout, index) => {
                    const payoutTime = new Date(payout.payout_time);
                    const timeLeft = payoutTime - new Date();
                    const minutesLeft = Math.max(0, Math.floor(timeLeft / 60000));

                    message += `${index + 1}. Memo: \`${payout.transfer_details.memo}\`\n`;
                    message += `   Amount: \`${payout.transfer_details.quantity}\`\n`;
                    message += `   Due in: \`${minutesLeft} minutes\`\n`;
                    message += `   Retries: \`${payout.retries || 0}\`\n\n`;
                });
                if (pendingPayouts.length > 5) {
                    message += `... and ${pendingPayouts.length - 5} more\n\n`;
                }
            } else {
                message += '*🕐 Pending Payouts:* None\n\n';
            }

            // Show failed payouts
            if (failedPayouts && failedPayouts.length > 0) {
                message += `*❌ Failed Payouts (${failedPayouts.length}):*\n`;
                failedPayouts.slice(0, 5).forEach((payout, index) => {
                    message += `${index + 1}. Memo: \`${payout.transfer_details.memo}\`\n`;
                    message += `   Amount: \`${payout.transfer_details.quantity}\`\n`;
                    message += `   From: \`${payout.transfer_details.from}\`\n`;
                    message += `   Retries: \`${payout.retries || 0}\`\n\n`;
                });
                if (failedPayouts.length > 5) {
                    message += `... and ${failedPayouts.length - 5} more\n\n`;
                }
                message += `Use \`/retrypayout <memo>\` to retry a specific failed payout.`;
            } else {
                message += '*❌ Failed Payouts:* None';
            }

            ctx.reply(message, { parse_mode: 'Markdown' });
        } catch (error) {
            console.error('[ERROR] Failed to get pending payouts:', error);
            ctx.reply('❌ Failed to get pending payouts. Check the logs.');
        }
    });

    // --- /retrypayout ---
    bot.command('retrypayout', adminOnly, async (ctx) => {
        console.log(`[Cmd] Received /retrypayout command from ${ctx.from.id}`);
        const memo = ctx.message.text.split(' ')[1]?.trim();
        if (!memo) {
            return ctx.reply('⚠️ Usage: /retrypayout <memo>\n\nExample: /retrypayout 10003050');
        }

        try {
            await ctx.reply('⏳ Attempting to retry payout...');
            const result = await retryPayoutForMemo(memo);

            if (result.success) {
                const message = `✅ *Payout Retry Successful!*\n\nMemo: \`${memo}\`\nTransaction ID: \`${result.transaction_id}\`\n\n[View on Waxplorer](https://wax.bloks.io/transaction/${result.transaction_id})`;
                ctx.reply(message, { parse_mode: 'Markdown' });
                await notifyAdmins(`🔄 Admin ${ctx.from.username} (@${ctx.from.id}) successfully retried payout for memo: \`${memo}\``, ctx.from.id);
            } else {
                ctx.reply(`❌ *Payout Retry Failed*\n\nMemo: \`${memo}\`\nReason: ${result.reason}`, { parse_mode: 'Markdown' });
            }
        } catch (error) {
            console.error(`[ERROR] Failed to retry payout for memo ${memo}:`, error);
            ctx.reply(`❌ An error occurred while retrying payout for memo \`${memo}\`. Check the logs for details.`);
        }
    });

    console.log(`[Instance ${instanceId || 'unknown'}] [OK] Admin commands initialized.`);
}

module.exports = { initializeCommands };