# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Database files
db.json
db.json.backup*

# Deployment packages (excluded from repo)
minimal-protected-deployment/
minimal-protected-deployment*.zip
test-protected/

# Test files
test_*.js
fund_test_account.js
simulate_admin_unblock.js

# Build scripts
build-for-client.sh

# Logs
logs/
*.log

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS generated files
.DS_Store
Thumbs.db
