#!/usr/bin/env node

/**
 * Create Minimal Protected Deployment Package
 * Light protection that maintains functionality while providing basic code protection
 */

const JavaScriptObfuscator = require('javascript-obfuscator');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

console.log('🔒 Creating MINIMAL PROTECTION deployment package...');

// Security configuration
const deploymentKey = crypto.randomBytes(32).toString('hex');
const clientId = crypto.randomBytes(8).toString('hex');
const securitySalt = crypto.randomBytes(16).toString('hex');

console.log(`📋 Client ID: ${clientId}`);
console.log(`🔑 Deployment Key: ${deploymentKey}`);
console.log(`🧂 Security Salt: ${securitySalt}`);

// Create deployment directory
const deployDir = 'minimal-protected-deployment';
if (fs.existsSync(deployDir)) {
    fs.rmSync(deployDir, { recursive: true, force: true });
}
fs.mkdirSync(deployDir, { recursive: true });
fs.mkdirSync(path.join(deployDir, 'src'), { recursive: true });

// Files to process
const sourceFiles = [
    'src/index.js',
    'src/blockchain.js', 
    'src/commands.js',
    'src/config.js',
    'src/db.js',
    'src/payout.js',
    'src/rules.js',
    'src/telegram.js',
    'src/utils.js',
    'src/wax.js',
    'src/store.js'
];

// MINIMAL obfuscation options - light protection that won't break functionality
const minimalObfuscationOptions = {
    compact: true,                          // Remove whitespace
    controlFlowFlattening: false,           // DISABLED - causes issues
    controlFlowFlatteningThreshold: 0,      // DISABLED
    numbersToExpressions: false,            // DISABLED - can break numbers
    simplify: true,                         // Safe simplification
    stringArrayShuffle: false,              // DISABLED - can cause issues
    splitStrings: false,                    // DISABLED - can break strings
    stringArrayThreshold: 0.5,              // Moderate string protection
    transformObjectKeys: false,             // DISABLED - can break object access
    unicodeEscapeSequence: false,           // Keep readable
    identifierNamesGenerator: 'mangled',    // Simple name mangling
    renameGlobals: false,                   // DISABLED - can break Node.js
    selfDefending: false,                   // DISABLED - causes crashes
    debugProtection: false,                 // DISABLED - causes issues
    debugProtectionInterval: 0,             // DISABLED
    disableConsoleOutput: false,            // DISABLED - need console for debugging
    domainLock: [],                         // No domain lock
    reservedNames: [
        // Keep ALL essential names to prevent breaking
        'exports', 'require', 'module', '__dirname', '__filename',
        'process', 'console', 'Buffer', 'global', 'setTimeout', 'setInterval',
        'clearTimeout', 'clearInterval', 'setImmediate', 'clearImmediate',
        'Promise', 'Error', 'JSON', 'Date', 'Math', 'Object', 'Array',
        'String', 'Number', 'Boolean', 'RegExp', 'Function',
        // Telegram/WAX specific
        'Telegraf', 'fetch', 'crypto', 'fs', 'path'
    ]
};

function minimalObfuscate(sourceCode) {
    try {
        console.log('🔄 Applying minimal obfuscation...');
        const result = JavaScriptObfuscator.obfuscate(sourceCode, minimalObfuscationOptions);
        return result.getObfuscatedCode();
    } catch (error) {
        console.warn('⚠️  Obfuscation failed, using basic minification:', error.message);
        // Fallback to basic minification only
        return sourceCode
            .replace(/\/\*[\s\S]*?\*\//g, '')  // Remove block comments
            .replace(/\/\/.*$/gm, '')           // Remove line comments
            .replace(/\s+/g, ' ')               // Compress whitespace
            .trim();
    }
}

// Create simple security wrapper (much lighter than the heavy version)
function createLightSecurityWrapper(obfuscatedCode, filename) {
    return `
// Light protection wrapper for ${filename}
require('dotenv').config();

// Basic deployment verification
if (process.env.DEPLOYMENT_KEY !== '${deploymentKey}') {
    console.error('Invalid deployment key');
    process.exit(1);
}

if (process.env.SECURITY_SALT !== '${securitySalt}') {
    console.error('Security verification failed');
    process.exit(1);
}

// Original code (lightly protected)
${obfuscatedCode}
`;
}

// Process each file
sourceFiles.forEach(file => {
    if (fs.existsSync(file)) {
        try {
            let content = fs.readFileSync(file, 'utf8');
            
            console.log(`🔄 Processing ${file}...`);
            
            // Apply minimal obfuscation
            const obfuscatedCode = minimalObfuscate(content);
            
            // Add light security wrapper
            const protectedCode = createLightSecurityWrapper(obfuscatedCode, file);
            
            const outputPath = path.join(deployDir, file);
            fs.writeFileSync(outputPath, protectedCode);
            
            console.log(`✅ Protected: ${file}`);
        } catch (error) {
            console.error(`❌ Failed to process ${file}:`, error.message);
        }
    }
});

// Create production package.json (fixed the recursive install issue)
const originalPackage = JSON.parse(fs.readFileSync('package.json', 'utf8'));
const productionPackage = {
    name: `${originalPackage.name}-minimal-protected-${clientId}`,
    version: originalPackage.version,
    description: "WAX Trading Bot - Minimal Protected Deployment",
    main: "src/index.js",
    scripts: {
        "start": "node src/index.js"
        // REMOVED the recursive install script that was causing the loop
    },
    dependencies: originalPackage.dependencies,
    engines: originalPackage.engines || {
        "node": ">=16.0.0"
    },
    license: "PROPRIETARY",
    private: true,
    author: "Minimal Protected Deployment",
    keywords: ["protected", "wax", "bot"]
};

fs.writeFileSync(
    path.join(deployDir, 'package.json'),
    JSON.stringify(productionPackage, null, 2)
);

// Create correct environment template with proper variable names
const envTemplate = `# Telegram Bot Token from BotFather
TELEGRAM_BOT_TOKEN=**********************************************

# Your personal Telegram User ID (the bot will only listen to you)
TELEGRAM_ADMIN_ID=**********

# The WAX account the bot will monitor for incoming transfers
WAX_VAULT_ACCOUNT=blockvestwax

# The WAX account that will send the payouts
WAX_PAYOUT_ACCOUNT=blockvestwax

# The private key for the WAX_PAYOUT_ACCOUNT (must have active permission)
WAX_PAYOUT_PRIVATE_KEY=PVT_K1_UkJpbyttisoQpbRuZToWzdsn1aCq2HpswKZtt7hcctYpfTow6

# WAX API endpoint
WAX_API_ENDPOINT=https://wax.greymass.com

# Database
DATABASE_PATH=./db.json

# Security Keys (DO NOT CHANGE)
DEPLOYMENT_KEY=${deploymentKey}
SECURITY_SALT=${securitySalt}
NODE_ENV=production
CLIENT_ID=${clientId}
`;

fs.writeFileSync(path.join(deployDir, '.env'), envTemplate);

// Create simple startup script with correct variable names
const startupScript = `#!/usr/bin/env node
// Light protection startup script
require('dotenv').config();

const crypto = require('crypto');
const fs = require('fs');

// Verify deployment integrity with correct variable names
const requiredVars = ['TELEGRAM_BOT_TOKEN', 'WAX_PAYOUT_PRIVATE_KEY', 'WAX_VAULT_ACCOUNT', 'TELEGRAM_ADMIN_ID', 'DEPLOYMENT_KEY', 'SECURITY_SALT'];
for(const v of requiredVars) {
    if(!process.env[v]) {
        console.error('Missing required environment variable:', v);
        process.exit(1);
    }
}

// Additional security checks
if (process.env.DEPLOYMENT_KEY !== '${deploymentKey}') {
    console.error('Invalid deployment key');
    process.exit(1);
}

if (process.env.SECURITY_SALT !== '${securitySalt}') {
    console.error('Security verification failed');
    process.exit(1);
}

console.log('✅ Security verification passed');
console.log('🚀 Starting protected WAX trading bot...');

// Start the main application
require('./src/index.js');
`;

fs.writeFileSync(path.join(deployDir, 'start.js'), startupScript);

// Copy database and other files
if (fs.existsSync('db.json')) {
    fs.copyFileSync('db.json', path.join(deployDir, 'db.json'));
    console.log('📄 Copied: db.json');
}

// Create deployment metadata
const metadata = {
    clientId,
    deploymentKey,
    securitySalt,
    buildDate: new Date().toISOString(),
    version: originalPackage.version,
    protection: 'minimal',
    obfuscated: true,
    selfDefending: false,
    domainLocked: false,
    functionalityPreserved: true
};

fs.writeFileSync(
    path.join(deployDir, '.deployment-info.json'),
    JSON.stringify(metadata, null, 2)
);

console.log('\n🎉 Minimal protected deployment created successfully!');
console.log(`📁 Location: ./${deployDir}/`);
console.log(`👤 Client ID: ${clientId}`);
console.log(`🔐 Deployment Key: ${deploymentKey}`);
console.log(`🧂 Security Salt: ${securitySalt}`);
console.log('\n📋 Protection Level: MINIMAL');
console.log('✅ Functionality preserved');
console.log('✅ Basic code obfuscation');
console.log('✅ Environment variable protection');
console.log('✅ No aggressive features that break code');
console.log('\n📋 Next steps:');
console.log('1. Test locally first: cd minimal-protected-deployment && npm install && npm start');
console.log('2. If working, zip and deploy to client');
