#!/usr/bin/env node

/**
 * Deployment Setup Script
 * Creates a secure deployment package with environment protection
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

console.log('🚀 Setting up secure deployment package...');

// Generate deployment key
const deploymentKey = crypto.randomBytes(32).toString('hex');

// Create deployment instructions
const deploymentInstructions = `
# 🔒 SECURE DEPLOYMENT INSTRUCTIONS

## IMPORTANT SECURITY NOTES:
1. This code is obfuscated and protected
2. Never share the source code or this deployment package
3. Environment variables contain sensitive data

## DEPLOYMENT STEPS:

### 1. Upload to Namecheap
- Upload the entire 'dist' folder to your hosting
- Set Node.js version to 18+ in cPanel

### 2. Environment Setup
Create a .env file with these variables:
\`\`\`
BOT_TOKEN=your_telegram_bot_token_here
WAX_PRIVATE_KEY=your_wax_private_key_here
WAX_ACCOUNT=your_wax_account_name
WAX_RPC_ENDPOINT=https://wax.greymass.com
ADMIN_CHAT_ID=your_telegram_chat_id
DATABASE_URL=sqlite:./bot.db
PAYOUT_DELAY_MIN=5
PAYOUT_DELAY_MAX=10
PROFIT_PERCENTAGE=1.5
MIN_AMOUNT=1.0
MAX_AMOUNT=1000.0
DAILY_LIMIT=5000.0
RATE_LIMIT_MINUTES=60
DEPLOYMENT_KEY=${deploymentKey}
\`\`\`

### 3. Install Dependencies
\`\`\`bash
cd dist
npm run install-deps
\`\`\`

### 4. Start the Bot
\`\`\`bash
npm start
\`\`\`

### 5. Process Management (Recommended)
Install PM2 for production:
\`\`\`bash
npm install -g pm2
pm2 start src/index.js --name "wax-bot"
pm2 startup
pm2 save
\`\`\`

## SECURITY FEATURES ENABLED:
✅ Code obfuscation with self-defending mechanisms
✅ Debug protection enabled
✅ Console output disabled in production
✅ String array shuffling
✅ Control flow flattening
✅ Identifier name mangling

## SUPPORT:
- Only provide support through official channels
- Never share source code for debugging
- Use logs and error messages for troubleshooting

## LICENSE:
This software is licensed for single-use deployment only.
Redistribution or reverse engineering is prohibited.

---
Generated on: ${new Date().toISOString()}
Deployment Key: ${deploymentKey}
`;

// Write deployment instructions
fs.writeFileSync('DEPLOYMENT_INSTRUCTIONS.md', deploymentInstructions);

// Create a deployment verification script
const verificationScript = `
const crypto = require('crypto');
const expectedKey = '${deploymentKey}';

function verifyDeployment() {
    const deploymentKey = process.env.DEPLOYMENT_KEY;
    
    if (!deploymentKey) {
        console.error('❌ DEPLOYMENT_KEY not found in environment variables');
        process.exit(1);
    }
    
    if (deploymentKey !== expectedKey) {
        console.error('❌ Invalid deployment key');
        process.exit(1);
    }
    
    console.log('✅ Deployment verification successful');
    return true;
}

module.exports = { verifyDeployment };
`;

fs.writeFileSync('scripts/verify-deployment.js', verificationScript);

// Create production startup script
const startupScript = `
#!/usr/bin/env node

// Production startup with verification
require('./scripts/verify-deployment').verifyDeployment();

// Start the bot
require('./src/index.js');
`;

fs.writeFileSync('start-production.js', startupScript);

console.log('✅ Deployment setup complete!');
console.log('📋 Check DEPLOYMENT_INSTRUCTIONS.md for client instructions');
console.log('🔑 Deployment key generated for verification');
