#!/usr/bin/env node

/**
 * Build Deployment Package
 * Creates a secure, obfuscated deployment ready for client server
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

console.log('🔒 Building secure deployment package...');

// Create deployment directory
const deployDir = 'deployment';
if (fs.existsSync(deployDir)) {
    fs.rmSync(deployDir, { recursive: true, force: true });
}
fs.mkdirSync(deployDir, { recursive: true });
fs.mkdirSync(path.join(deployDir, 'src'), { recursive: true });

// Generate unique deployment key
const deploymentKey = crypto.randomBytes(32).toString('hex');
const clientId = crypto.randomBytes(8).toString('hex');

console.log(`📋 Client ID: ${clientId}`);
console.log(`🔑 Deployment Key: ${deploymentKey}`);

// Files to copy and potentially obfuscate
const sourceFiles = [
    'src/index.js',
    'src/blockchain.js', 
    'src/commands.js',
    'src/config.js',
    'src/db.js',
    'src/payout.js',
    'src/rules.js',
    'src/telegram.js',
    'src/utils.js',
    'src/wax.js',
    'src/store.js'
];

// Professional obfuscation using javascript-obfuscator
const JavaScriptObfuscator = require('javascript-obfuscator');

// Maximum protection obfuscation options
const obfuscationOptions = {
    compact: true,
    controlFlowFlattening: true,
    controlFlowFlatteningThreshold: 1,
    numbersToExpressions: true,
    simplify: true,
    stringArrayShuffle: true,
    splitStrings: true,
    stringArrayThreshold: 1,
    transformObjectKeys: true,
    unicodeEscapeSequence: false,
    identifierNamesGenerator: 'hexadecimal',
    renameGlobals: false,
    selfDefending: true,
    debugProtection: true,
    debugProtectionInterval: 2000,
    disableConsoleOutput: true,
    domainLock: [], // Can add client domain for extra security
    reservedNames: [
        // Keep essential Node.js globals
        'exports', 'require', 'module', '__dirname', '__filename',
        'process', 'console', 'Buffer', 'global', 'setTimeout', 'setInterval'
    ]
};

function obfuscateCode(sourceCode) {
    try {
        const result = JavaScriptObfuscator.obfuscate(sourceCode, obfuscationOptions);
        return result.getObfuscatedCode();
    } catch (error) {
        console.warn('Obfuscation failed, using minified version:', error.message);
        // Fallback to basic minification
        return sourceCode
            .replace(/\/\*[\s\S]*?\*\//g, '')
            .replace(/\/\/.*$/gm, '')
            .replace(/\s+/g, ' ')
            .trim();
    }
}

// Copy and process source files
sourceFiles.forEach(file => {
    if (fs.existsSync(file)) {
        try {
            let content = fs.readFileSync(file, 'utf8');
            
            // Add deployment verification to main files
            if (file === 'src/index.js') {
                content = content.replace(
                    'checkIntegrity();',
                    `checkIntegrity();\n// Deployment verification\nif(process.env.DEPLOYMENT_KEY !== '${deploymentKey}') process.exit(1);`
                );
            }
            
            // Apply professional obfuscation
            const obfuscatedContent = obfuscateCode(content);
            
            const outputPath = path.join(deployDir, file);
            fs.writeFileSync(outputPath, obfuscatedContent);
            
            console.log(`✅ Processed: ${file}`);
        } catch (error) {
            console.error(`❌ Failed to process ${file}:`, error.message);
        }
    }
});

// Create production package.json
const originalPackage = JSON.parse(fs.readFileSync('package.json', 'utf8'));
const productionPackage = {
    name: `${originalPackage.name}-client-${clientId}`,
    version: originalPackage.version,
    description: "WAX Trading Bot - Client Deployment",
    main: "src/index.js",
    scripts: {
        "start": "node src/index.js",
        "install": "npm install --production --no-optional"
    },
    dependencies: originalPackage.dependencies,
    engines: originalPackage.engines || {
        "node": ">=16.0.0"
    },
    license: "PROPRIETARY",
    private: true
};

fs.writeFileSync(
    path.join(deployDir, 'package.json'),
    JSON.stringify(productionPackage, null, 2)
);

// Create environment template
const envTemplate = `# WAX Trading Bot Configuration
# IMPORTANT: Fill in all values before starting the bot

# Telegram Bot Configuration
BOT_TOKEN=your_telegram_bot_token_here
ADMIN_CHAT_ID=your_telegram_chat_id_here

# WAX Blockchain Configuration  
WAX_PRIVATE_KEY=your_wax_private_key_here
WAX_ACCOUNT=your_wax_account_name_here
WAX_RPC_ENDPOINT=https://wax.greymass.com
WAX_PAYOUT_DESTINATION=bybitwaxonly

# Bot Settings
PROFIT_PERCENTAGE=1.5
PAYOUT_DELAY_MIN=5
PAYOUT_DELAY_MAX=10

# Security (DO NOT CHANGE)
DEPLOYMENT_KEY=${deploymentKey}
NODE_ENV=production
CLIENT_ID=${clientId}

# Database
DATABASE_PATH=./data/bot.db
`;

fs.writeFileSync(path.join(deployDir, '.env.example'), envTemplate);

// Create deployment instructions
const instructions = `# 🚀 WAX Trading Bot - Deployment Instructions

## Client ID: ${clientId}
## Deployment Date: ${new Date().toISOString()}

### IMPORTANT SECURITY NOTES:
⚠️  This is a PROTECTED deployment package
⚠️  Do NOT share this package with anyone
⚠️  Do NOT attempt to reverse engineer the code
⚠️  Support is provided only through official channels

### DEPLOYMENT STEPS:

#### 1. Upload to Namecheap Hosting
- Upload all files to your hosting directory
- Ensure Node.js 16+ is enabled in cPanel

#### 2. Environment Configuration
- Copy \`.env.example\` to \`.env\`
- Fill in all required values:
  - BOT_TOKEN: Your Telegram bot token
  - ADMIN_CHAT_ID: Your Telegram chat ID
  - WAX_PRIVATE_KEY: Your WAX private key
  - WAX_ACCOUNT: Your WAX account name

#### 3. Install Dependencies
\`\`\`bash
npm install
\`\`\`

#### 4. Start the Bot
\`\`\`bash
npm start
\`\`\`

#### 5. Production Management (Recommended)
\`\`\`bash
# Install PM2 for process management
npm install -g pm2

# Start with PM2
pm2 start src/index.js --name "wax-bot"

# Auto-restart on server reboot
pm2 startup
pm2 save
\`\`\`

### FEATURES INCLUDED:
✅ Complete WAX trading bot functionality
✅ Telegram admin interface
✅ Automatic profit calculations (1.5%)
✅ Advanced blocking rules
✅ Transaction monitoring
✅ Payout scheduling
✅ Admin commands

### SUPPORT:
- Contact developer for technical support
- Provide logs and error messages for troubleshooting
- Do NOT share source code or deployment files

### LICENSE:
This software is licensed for single-use only.
Redistribution, modification, or reverse engineering is prohibited.

---
Package built on: ${new Date().toISOString()}
`;

fs.writeFileSync(path.join(deployDir, 'README.md'), instructions);

// Create a simple startup verification script
const startupScript = `#!/usr/bin/env node
// Deployment verification and startup
const requiredVars = ['BOT_TOKEN', 'WAX_PRIVATE_KEY', 'WAX_ACCOUNT', 'ADMIN_CHAT_ID', 'DEPLOYMENT_KEY'];
for(const v of requiredVars) {
    if(!process.env[v]) {
        console.error('Missing required environment variable:', v);
        process.exit(1);
    }
}
console.log('✅ Environment verified, starting bot...');
require('./src/index.js');
`;

fs.writeFileSync(path.join(deployDir, 'start.js'), startupScript);

// Create package info file
const packageInfo = {
    clientId,
    deploymentKey,
    buildDate: new Date().toISOString(),
    version: originalPackage.version,
    protected: true
};

fs.writeFileSync(
    path.join(deployDir, '.package-info.json'),
    JSON.stringify(packageInfo, null, 2)
);

console.log('\n🎉 Deployment package created successfully!');
console.log(`📁 Location: ./${deployDir}/`);
console.log(`👤 Client ID: ${clientId}`);
console.log(`🔐 Deployment Key: ${deploymentKey}`);
console.log('\n📋 Next steps:');
console.log('1. Zip the deployment folder');
console.log('2. Send to client with instructions');
console.log('3. Keep the deployment key for support');
console.log('\n⚠️  IMPORTANT: Keep the original source code secure!');
