#!/usr/bin/env node

/**
 * Create Protected Deployment Package
 * Maximum security deployment with multiple protection layers
 */

const JavaScriptObfuscator = require('javascript-obfuscator');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

console.log('🔒 Creating MAXIMUM SECURITY deployment package...');

// Security configuration
const clientDomain = process.argv[2] || ''; // Optional domain lock
const expirationDays = parseInt(process.argv[3]) || 0; // Optional expiration
const deploymentKey = crypto.randomBytes(32).toString('hex');
const clientId = crypto.randomBytes(8).toString('hex');
const securitySalt = crypto.randomBytes(16).toString('hex');

console.log(`👤 Client ID: ${clientId}`);
console.log(`🔑 Deployment Key: ${deploymentKey}`);
if (clientDomain) console.log(`🌐 Domain Lock: ${clientDomain}`);
if (expirationDays) console.log(`⏰ Expires in: ${expirationDays} days`);

// Create deployment directory
const deployDir = 'protected-deployment';
if (fs.existsSync(deployDir)) {
    fs.rmSync(deployDir, { recursive: true, force: true });
}
fs.mkdirSync(deployDir, { recursive: true });
fs.mkdirSync(path.join(deployDir, 'src'), { recursive: true });

// Maximum security obfuscation options
const obfuscationOptions = {
    compact: true,
    controlFlowFlattening: true,
    controlFlowFlatteningThreshold: 1,
    numbersToExpressions: true,
    simplify: true,
    stringArrayShuffle: true,
    splitStrings: true,
    stringArrayThreshold: 1,
    transformObjectKeys: true,
    unicodeEscapeSequence: false,
    identifierNamesGenerator: 'hexadecimal',
    renameGlobals: false,
    selfDefending: true,
    debugProtection: true,
    debugProtectionInterval: 2000,
    disableConsoleOutput: true,
    domainLock: clientDomain ? [clientDomain] : [],
    reservedNames: [
        'exports', 'require', 'module', '__dirname', '__filename',
        'process', 'console', 'Buffer', 'global', 'setTimeout', 'setInterval',
        'clearTimeout', 'clearInterval', 'setImmediate', 'clearImmediate'
    ]
};

// Create security wrapper
function createSecurityWrapper(originalCode, filename) {
    const expirationCheck = expirationDays ? `
        const deployDate = new Date('${new Date().toISOString()}');
        const expirationDate = new Date(deployDate.getTime() + ${expirationDays * 24 * 60 * 60 * 1000});
        if (new Date() > expirationDate) {
            console.error('License expired');
            process.exit(1);
        }
    ` : '';

    const domainCheck = clientDomain ? `
        const os = require('os');
        const hostname = os.hostname();
        if (!hostname.includes('${clientDomain}') && !process.env.BYPASS_DOMAIN_CHECK) {
            console.error('Invalid deployment environment');
            process.exit(1);
        }
    ` : '';

    return `
(function() {
    'use strict';
    
    // Anti-debugging protection
    const originalConsole = console;
    if (process.env.NODE_ENV === 'production') {
        console.log = console.warn = console.error = console.debug = () => {};
    }
    
    // Runtime integrity checks
    function securityCheck() {
        ${expirationCheck}
        ${domainCheck}
        
        // Deployment key verification
        if (process.env.DEPLOYMENT_KEY !== '${deploymentKey}') {
            process.exit(1);
        }
        
        // Security salt verification
        if (process.env.SECURITY_SALT !== '${securitySalt}') {
            process.exit(1);
        }
        
        return true;
    }
    
    // Run security check
    securityCheck();
    
    // Original code (obfuscated)
    ${originalCode}
    
})();
    `;
}

// Files to process
const sourceFiles = [
    'src/index.js',
    'src/blockchain.js',
    'src/commands.js',
    'src/config.js',
    'src/db.js',
    'src/payout.js',
    'src/rules.js',
    'src/telegram.js',
    'src/utils.js',
    'src/wax.js',
    'src/store.js'
];

// Process each file
sourceFiles.forEach(file => {
    if (fs.existsSync(file)) {
        try {
            let content = fs.readFileSync(file, 'utf8');
            
            // First obfuscate the code
            console.log(`🔄 Obfuscating ${file}...`);
            const obfuscatedCode = JavaScriptObfuscator.obfuscate(content, obfuscationOptions);
            
            // Then wrap with security layer
            const secureCode = createSecurityWrapper(obfuscatedCode.getObfuscatedCode(), file);
            
            const outputPath = path.join(deployDir, file);
            fs.writeFileSync(outputPath, secureCode);
            
            console.log(`✅ Protected: ${file}`);
        } catch (error) {
            console.error(`❌ Failed to protect ${file}:`, error.message);
        }
    }
});

// Create production package.json
const originalPackage = JSON.parse(fs.readFileSync('package.json', 'utf8'));
const productionPackage = {
    name: `${originalPackage.name}-protected-${clientId}`,
    version: originalPackage.version,
    description: "WAX Trading Bot - Protected Client Deployment",
    main: "src/index.js",
    scripts: {
        "start": "node src/index.js",
        "install": "npm install --production --no-optional"
    },
    dependencies: originalPackage.dependencies,
    engines: originalPackage.engines || {
        "node": ">=16.0.0"
    },
    license: "PROPRIETARY",
    private: true,
    author: "Protected Deployment",
    keywords: ["protected", "obfuscated", "wax", "bot"]
};

// Remove dev dependencies
delete productionPackage.devDependencies;

fs.writeFileSync(
    path.join(deployDir, 'package.json'),
    JSON.stringify(productionPackage, null, 2)
);

// Create secure environment template
const envTemplate = `# WAX Trading Bot - Protected Deployment
# Client ID: ${clientId}
# Generated: ${new Date().toISOString()}

# Telegram Configuration
BOT_TOKEN=your_telegram_bot_token_here
ADMIN_CHAT_ID=your_telegram_chat_id_here

# WAX Blockchain Configuration
WAX_PRIVATE_KEY=your_wax_private_key_here
WAX_ACCOUNT=your_wax_account_name_here
WAX_RPC_ENDPOINT=https://wax.greymass.com
WAX_PAYOUT_DESTINATION=bybitwaxonly

# Bot Settings
PROFIT_PERCENTAGE=1.5
PAYOUT_DELAY_MIN=5
PAYOUT_DELAY_MAX=10

# Security Keys (DO NOT MODIFY)
DEPLOYMENT_KEY=${deploymentKey}
SECURITY_SALT=${securitySalt}
NODE_ENV=production
CLIENT_ID=${clientId}

# Database
DATABASE_PATH=./data/bot.db
`;

fs.writeFileSync(path.join(deployDir, '.env.example'), envTemplate);

// Create deployment documentation
const documentation = `# 🔒 PROTECTED WAX TRADING BOT DEPLOYMENT

## ⚠️ SECURITY NOTICE
This is a MAXIMUM SECURITY protected deployment package.
- Code is heavily obfuscated and self-defending
- Multiple anti-tampering mechanisms active
- Unauthorized access attempts will terminate the application
- Support is provided ONLY through official channels

## Client Information
- **Client ID:** ${clientId}
- **Deployment Date:** ${new Date().toISOString()}
${clientDomain ? `- **Domain Lock:** ${clientDomain}` : ''}
${expirationDays ? `- **License Expires:** ${new Date(Date.now() + expirationDays * 24 * 60 * 60 * 1000).toISOString()}` : ''}

## Deployment Instructions

### 1. Server Requirements
- Node.js 16+ (recommended: 18+)
- Minimum 1GB RAM
- Stable internet connection
- Linux/Unix environment (recommended)

### 2. Upload Files
- Upload ALL files to your server
- Maintain directory structure
- Set proper file permissions (644 for files, 755 for directories)

### 3. Environment Setup
\`\`\`bash
cp .env.example .env
nano .env  # Fill in your configuration
\`\`\`

### 4. Install Dependencies
\`\`\`bash
npm install --production
\`\`\`

### 5. Start the Bot
\`\`\`bash
npm start
\`\`\`

### 6. Production Deployment (Recommended)
\`\`\`bash
# Install PM2 for process management
npm install -g pm2

# Start with PM2
pm2 start src/index.js --name "wax-bot"

# Enable auto-restart
pm2 startup
pm2 save

# Monitor logs
pm2 logs wax-bot
\`\`\`

## Security Features
✅ Advanced code obfuscation
✅ Self-defending mechanisms
✅ Debug protection
✅ Console output disabled
✅ String array shuffling
✅ Control flow flattening
✅ Runtime integrity checks
${clientDomain ? '✅ Domain locking enabled' : ''}
${expirationDays ? '✅ License expiration protection' : ''}

## Troubleshooting
- Check all environment variables are set correctly
- Ensure Node.js version is 16+
- Verify network connectivity to WAX blockchain
- Contact support with error logs (never share source code)

## Support Policy
- Technical support provided for 30 days
- Configuration assistance included
- Source code is NOT provided for debugging
- Remote assistance available if needed

## Legal Notice
This software is licensed for single-use deployment only.
- Redistribution is prohibited
- Reverse engineering is prohibited
- Modification attempts will void support
- All rights reserved

---
Protected deployment package generated on ${new Date().toISOString()}
`;

fs.writeFileSync(path.join(deployDir, 'README.md'), documentation);

// Create startup script with additional checks
const startupScript = `#!/usr/bin/env node
// Protected startup script
const crypto = require('crypto');
const fs = require('fs');

// Verify deployment integrity
const requiredVars = ['BOT_TOKEN', 'WAX_PRIVATE_KEY', 'WAX_ACCOUNT', 'ADMIN_CHAT_ID', 'DEPLOYMENT_KEY', 'SECURITY_SALT'];
for(const v of requiredVars) {
    if(!process.env[v]) {
        console.error('Missing required environment variable:', v);
        process.exit(1);
    }
}

// Additional security checks
if (process.env.DEPLOYMENT_KEY !== '${deploymentKey}') {
    console.error('Invalid deployment key');
    process.exit(1);
}

if (process.env.SECURITY_SALT !== '${securitySalt}') {
    console.error('Security verification failed');
    process.exit(1);
}

console.log('✅ Security verification passed');
console.log('🚀 Starting protected WAX trading bot...');

// Start the main application
require('./src/index.js');
`;

fs.writeFileSync(path.join(deployDir, 'start.js'), startupScript);

// Create deployment metadata
const metadata = {
    clientId,
    deploymentKey,
    securitySalt,
    buildDate: new Date().toISOString(),
    version: originalPackage.version,
    protection: 'maximum',
    obfuscated: true,
    selfDefending: true,
    domainLocked: !!clientDomain,
    domain: clientDomain || null,
    expirationDays: expirationDays || null,
    expirationDate: expirationDays ? new Date(Date.now() + expirationDays * 24 * 60 * 60 * 1000).toISOString() : null
};

fs.writeFileSync(
    path.join(deployDir, '.deployment-info.json'),
    JSON.stringify(metadata, null, 2)
);

console.log('\n🎉 PROTECTED deployment package created successfully!');
console.log(`📁 Location: ./${deployDir}/`);
console.log(`👤 Client ID: ${clientId}`);
console.log(`🔐 Deployment Key: ${deploymentKey}`);
console.log(`🧂 Security Salt: ${securitySalt}`);
console.log('\n🔒 Security Features Enabled:');
console.log('  ✅ Maximum obfuscation');
console.log('  ✅ Self-defending code');
console.log('  ✅ Debug protection');
console.log('  ✅ Runtime integrity checks');
console.log('  ✅ Anti-tampering mechanisms');
if (clientDomain) console.log(`  ✅ Domain locked to: ${clientDomain}`);
if (expirationDays) console.log(`  ✅ License expires in: ${expirationDays} days`);

console.log('\n📋 Next Steps:');
console.log('1. Test the deployment locally');
console.log('2. Create deployment archive');
console.log('3. Send to client with README.md');
console.log('4. Keep deployment keys secure for support');
console.log('\n⚠️  CRITICAL: Never share the original source code!');
