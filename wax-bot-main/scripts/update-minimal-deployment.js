#!/usr/bin/env node

/**
 * Update Minimal Protected Deployment with fixes
 * Applies light obfuscation while preserving functionality
 */

const JavaScriptObfuscator = require('javascript-obfuscator');
const fs = require('fs');
const path = require('path');

console.log('🔧 Updating minimal protected deployment with fixes...');

// Light obfuscation options (preserves functionality)
const lightObfuscationOptions = {
    compact: false,
    controlFlowFlattening: false,
    numbersToExpressions: false,
    simplify: false,
    stringArrayShuffle: false,
    splitStrings: false,
    stringArrayThreshold: 0.3,
    transformObjectKeys: false,
    unicodeEscapeSequence: false,
    identifierNamesGenerator: 'mangled',
    renameGlobals: false,
    selfDefending: false,
    debugProtection: false,
    deadCodeInjection: false,
    disableConsoleOutput: false
};

// Security wrapper template
function createSecurityWrapper(originalCode, filename) {
    return `
// Light protection wrapper for ${filename}
require('dotenv').config();

// Basic deployment verification
if (process.env.DEPLOYMENT_KEY !== '28cbffd1acebd0719f7a691467ef3ed6fc1a7843d34492c80422627af7401228') {
    console.error('Invalid deployment key');
    process.exit(1);
}

if (process.env.SECURITY_SALT !== 'ee032aef6d0e26287f9791437a86ca8d') {
    console.error('Security verification failed');
    process.exit(1);
}

// Original code (lightly protected) - FIXED: Database caching and blockchain polling issues
${originalCode}
`;
}

// Files to process
const filesToProcess = [
    'src/db.js',
    'src/blockchain.js', 
    'src/commands.js'
];

// Process each file
filesToProcess.forEach(filePath => {
    console.log(`🔄 Updating ${filePath}...`);
    
    const sourceFile = path.join('src', path.basename(filePath));
    const targetFile = path.join('minimal-protected-deployment', filePath);
    
    if (!fs.existsSync(sourceFile)) {
        console.error(`❌ Source file not found: ${sourceFile}`);
        return;
    }
    
    try {
        // Read the fixed source code
        const sourceCode = fs.readFileSync(sourceFile, 'utf8');
        
        // Apply light obfuscation
        const obfuscated = JavaScriptObfuscator.obfuscate(sourceCode, lightObfuscationOptions);
        
        // Wrap with security
        const protectedCode = createSecurityWrapper(obfuscated.getObfuscatedCode(), filePath);
        
        // Write to target
        fs.writeFileSync(targetFile, protectedCode);
        console.log(`✅ Updated: ${filePath}`);
        
    } catch (error) {
        console.error(`❌ Failed to process ${filePath}:`, error.message);
    }
});

console.log('\n🎉 Minimal protected deployment updated with fixes!');
console.log('📁 Location: ./minimal-protected-deployment/');
console.log('\n🔧 Fixes Applied:');
console.log('  ✅ Database caching issue fixed (getMemo now async with db.read())');
console.log('  ✅ Blockchain polling optimized for immediate response');
console.log('  ✅ Admin unblock triggers immediate blockchain check');
console.log('\n⚡ Result: Users can trade IMMEDIATELY after admin unblock!');
