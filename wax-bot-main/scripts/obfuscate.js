const JavaScriptObfuscator = require('javascript-obfuscator');
const fs = require('fs');
const path = require('path');

// Configuration for maximum obfuscation
const obfuscationOptions = {
    compact: true,
    controlFlowFlattening: true,
    controlFlowFlatteningThreshold: 1,
    numbersToExpressions: true,
    simplify: true,
    stringArrayShuffle: true,
    splitStrings: true,
    stringArrayThreshold: 1,
    transformObjectKeys: true,
    unicodeEscapeSequence: false,
    identifierNamesGenerator: 'hexadecimal',
    renameGlobals: false,
    selfDefending: true,
    debugProtection: true,
    debugProtectionInterval: 2000,
    disableConsoleOutput: true,
    domainLock: [], // Add client's domain here if needed
    reservedNames: [
        // Keep these function names readable for debugging
        'exports', 'require', 'module', '__dirname', '__filename'
    ]
};

// Files to obfuscate
const filesToObfuscate = [
    'src/index.js',
    'src/blockchain.js',
    'src/commands.js',
    'src/db.js',
    'src/payout.js',
    'src/rules.js',
    'src/telegram.js',
    'src/utils.js',
    'src/wax.js',
    'src/store.js'
];

// Create obfuscated directory
const obfuscatedDir = 'dist';
if (!fs.existsSync(obfuscatedDir)) {
    fs.mkdirSync(obfuscatedDir, { recursive: true });
}

// Copy src structure to dist
fs.mkdirSync(path.join(obfuscatedDir, 'src'), { recursive: true });

console.log('🔒 Starting code obfuscation...');

filesToObfuscate.forEach(file => {
    try {
        const sourceCode = fs.readFileSync(file, 'utf8');
        const obfuscatedCode = JavaScriptObfuscator.obfuscate(sourceCode, obfuscationOptions);
        
        const outputPath = path.join(obfuscatedDir, file);
        fs.writeFileSync(outputPath, obfuscatedCode.getObfuscatedCode());
        
        console.log(`✅ Obfuscated: ${file} -> ${outputPath}`);
    } catch (error) {
        console.error(`❌ Failed to obfuscate ${file}:`, error.message);
    }
});

// Copy other necessary files without obfuscation
const filesToCopy = [
    'package.json',
    '.env.example',
    'README.md'
];

filesToCopy.forEach(file => {
    if (fs.existsSync(file)) {
        fs.copyFileSync(file, path.join(obfuscatedDir, file));
        console.log(`📄 Copied: ${file}`);
    }
});

// Create a modified package.json for production
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
packageJson.main = 'src/index.js';
packageJson.scripts = {
    "start": "node src/index.js",
    "install-deps": "npm install --production"
};

// Remove dev dependencies to reduce size
delete packageJson.devDependencies;

fs.writeFileSync(
    path.join(obfuscatedDir, 'package.json'), 
    JSON.stringify(packageJson, null, 2)
);

console.log('🎉 Obfuscation complete! Deploy the "dist" folder to your client.');
console.log('⚠️  Remember: Keep the original source code safe and never share it!');
