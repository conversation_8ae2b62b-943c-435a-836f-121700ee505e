const { Api, JsonRpc, RpcError } = require('eosjs');
const { JsSignatureProvider } = require('eosjs/dist/eosjs-jssig');
const fetch = require('node-fetch');
const { TextEncoder, TextDecoder } = require('util');

// --- WETRADEWAX13 ACCOUNT CONFIGURATION ---
const ACCOUNT_NAME = 'wetradewax13';
const PRIVATE_KEY = 'PVT_K1_UkJpbyttisoQpbRuZToWzdsn1aCq2HpswKZtt7hcctYpfTow6';
const PUBLIC_KEY = 'EOS89FbVL95kZv5BHThMhrSSWcPfgk4uQPnCDAoLRoRm8kRfxK3YS';
const RPC_ENDPOINT = 'https://wax.greymass.com';

// --- UTILITY FUNCTIONS ---
const signatureProvider = new JsSignatureProvider([PRIVATE_KEY]);
const rpc = new JsonRpc(RPC_ENDPOINT, { fetch });
const api = new Api({ rpc, signatureProvider, textDecoder: new TextDecoder(), textEncoder: new TextEncoder() });

async function checkAccountStatus() {
    try {
        console.log(`🔍 Checking ${ACCOUNT_NAME} status...\n`);
        
        const account = await rpc.get_account(ACCOUNT_NAME);
        const balance = await rpc.get_currency_balance('eosio.token', ACCOUNT_NAME, 'WAX');
        
        console.log('✅ Account Status:');
        console.log(`Account: ${ACCOUNT_NAME}`);
        console.log(`Balance: ${balance.length > 0 ? balance[0] : '0.******** WAX'}`);
        console.log(`RAM: ${account.ram_quota} bytes (available: ${account.ram_quota - account.ram_usage})`);
        console.log(`CPU: ${account.cpu_limit.max} microseconds`);
        console.log(`NET: ${account.net_limit.max} bytes`);
        console.log(`\n🔗 View: https://wax.bloks.io/account/${ACCOUNT_NAME}`);
        
        return { account, balance: balance.length > 0 ? balance[0] : '0.******** WAX' };
    } catch (error) {
        console.error('❌ Error checking account:', error.message);
        return null;
    }
}

async function sendWax(toAccount, amount, memo = '') {
    try {
        console.log(`💸 Sending ${amount} WAX from ${ACCOUNT_NAME} to ${toAccount}...`);
        
        const result = await api.transact({
            actions: [{
                account: 'eosio.token',
                name: 'transfer',
                authorization: [{
                    actor: ACCOUNT_NAME,
                    permission: 'active',
                }],
                data: {
                    from: ACCOUNT_NAME,
                    to: toAccount,
                    quantity: amount,
                    memo: memo,
                },
            }]
        }, {
            blocksBehind: 3,
            expireSeconds: 30,
        });

        console.log('✅ Transfer successful!');
        console.log(`Transaction ID: ${result.transaction_id}`);
        console.log(`View: https://wax.bloks.io/transaction/${result.transaction_id}`);
        
        return result;
    } catch (e) {
        console.error('❌ Transfer failed!');
        if (e instanceof RpcError) {
            console.error(JSON.stringify(e.json, null, 2));
        } else {
            console.error(e);
        }
        return null;
    }
}

// --- MAIN FUNCTION ---
async function main() {
    const command = process.argv[2];
    
    if (!command) {
        console.log('🔧 WETRADEWAX13 Account Manager\n');
        console.log('Usage:');
        console.log('  node manage-wetradewax13.js status          - Check account status');
        console.log('  node manage-wetradewax13.js send <to> <amount> [memo] - Send WAX');
        console.log('\nExamples:');
        console.log('  node manage-wetradewax13.js status');
        console.log('  node manage-wetradewax13.js send blockvestwax "10.******** WAX" "test transfer"');
        console.log('\n📋 Account Details:');
        console.log(`Account: ${ACCOUNT_NAME}`);
        console.log(`Private Key: ${PRIVATE_KEY}`);
        console.log(`Public Key: ${PUBLIC_KEY}`);
        return;
    }
    
    switch (command.toLowerCase()) {
        case 'status':
            await checkAccountStatus();
            break;
            
        case 'send':
            const toAccount = process.argv[3];
            const amount = process.argv[4];
            const memo = process.argv[5] || '';
            
            if (!toAccount || !amount) {
                console.error('❌ Usage: node manage-wetradewax13.js send <to> <amount> [memo]');
                return;
            }
            
            await sendWax(toAccount, amount, memo);
            break;
            
        default:
            console.error('❌ Unknown command. Use "status" or "send"');
    }
}

// Export functions for use in other scripts
module.exports = {
    ACCOUNT_NAME,
    PRIVATE_KEY,
    PUBLIC_KEY,
    checkAccountStatus,
    sendWax
};

// Run if called directly
if (require.main === module) {
    main();
}
