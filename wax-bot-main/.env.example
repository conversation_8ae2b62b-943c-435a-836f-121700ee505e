# Telegram <PERSON><PERSON> Token from BotFather
TELEGRAM_BOT_TOKEN=

# Your personal Telegram User ID (the bot will only listen to you)
TELEGRAM_ADMIN_ID=

# The WAX account the bot will monitor for incoming transfers
WAX_VAULT_ACCOUNT=waxprofitnew

# The WAX account that will send the payouts
WAX_PAYOUT_ACCOUNT=bybitwaxonly

# The private key for the WAX_PAYOUT_ACCOUNT (must have active permission)
WAX_PAYOUT_PRIVATE_KEY=

# WAX API endpoint
WAX_API_ENDPOINT=https://wax.greymass.com


# Telegram Bot Token from BotFather
TELEGRAM_BOT_TOKEN=**********************************************

# Your personal Telegram User ID (the bot will only listen to you)
TELEGRAM_ADMIN_ID=**********

# The WAX account the bot will monitor for incoming transfers
WAX_VAULT_ACCOUNT=blockvestwax

# The WAX account that will send the payouts
WAX_PAYOUT_ACCOUNT=blockvestwax

# The private key for the WAX_PAYOUT_ACCOUNT (must have active permission)
WAX_PAYOUT_PRIVATE_KEY=PVT_K1_UkJpbyttisoQpbRuZToWzdsn1aCq2HpswKZtt7hcctYpfTow6

# WAX API endpoint
WAX_API_ENDPOINT=https://wax.greymass.com
