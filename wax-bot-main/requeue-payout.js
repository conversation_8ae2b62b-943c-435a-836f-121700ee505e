const db = require('./src/db');
const { formatISO } = require('date-fns');

// --- CONFIGURATION ---
const memoToRequeue = '10003050';

// --- SCRIPT ---
async function main() {
    // Manually initialize the DB for this standalone script, which the main bot does on startup.
    await db.initializeDb();
    console.log('Database initialized.');

    console.log(`Attempting to re-queue payout for memo: ${memoToRequeue}`);

    // Find the latest trade with this memo using the correct DB function
    const trades = db.getTradesByMemo(memoToRequeue);
    if (!trades || trades.length === 0) {
        console.error(`Error: Could not find any trade with memo '${memoToRequeue}'.`);
        return;
    }
    // Get the most recent one in case of duplicates
    const trade = trades[trades.length - 1];

    const originalTxId = trade.transaction_id;
    console.log(`Found original trade with transaction ID: ${originalTxId}`);

    // Check if it's already in the pending queue
    const pendingPayouts = db.getPendingPayouts();
    const isPending = pendingPayouts.some(p => p.transaction_id === originalTxId);

    if (isPending) {
        console.error('Error: This payout is already in the pending queue. No action needed.');
        return;
    }

    // NOTE: Skipping check for failed_payouts as there's no getter and it was empty.

    // Add it back to the pending queue to be paid immediately
    const payoutTime = formatISO(new Date()); // Set to pay now
    const payoutObject = {
        transaction_id: trade.transaction_id,
        payout_time: payoutTime,
        transfer_details: trade
    };

    await db.addPendingPayout(payoutObject);

    console.log(`✅ Successfully re-queued payout for memo: ${trade.memo}`);
    console.log('The bot will process this payout within the next 30 seconds.');
}

main().catch(err => {
    console.error('An unexpected error occurred:');
    console.error(err);
});
