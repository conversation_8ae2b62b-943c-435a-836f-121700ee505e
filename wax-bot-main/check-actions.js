const { JsonRpc } = require('eosjs');
const fetch = require('node-fetch');

// --- Configuration ---
const accountName = 'blockvestwax';
const rpcEndpoint = 'https://wax.greymass.com';

// --- Script ---
const rpc = new JsonRpc(rpcEndpoint, { fetch });

async function main() {
    try {
        console.log(`Fetching last 10 actions for account: ${accountName}...\n`);

        const result = await rpc.history_get_actions(accountName, -1, -10);

        if (!result || !result.actions || result.actions.length === 0) {
            console.log('No actions found for this account.');
            return;
        }

        console.log('--- Last 10 Actions (newest first) ---');
        result.actions.reverse().forEach(actionWrapper => {
            const action = actionWrapper.action_trace;
            const txId = action.trx_id.substring(0, 8);
            const timestamp = new Date(action.block_time + 'Z').toLocaleString(); // Add Z for UTC for correct parsing
            const { from, to, quantity, memo } = action.act.data;

            if (action.act.name === 'transfer') {
                 console.log(`${txId} | ${timestamp} | ${from} -> ${to} | ${quantity} | Memo: "${memo}"`);
            }
        });

    } catch (error) {
        console.error('Error fetching account actions:', error.json ? JSON.stringify(error.json, null, 2) : error);
    }
}

main();
